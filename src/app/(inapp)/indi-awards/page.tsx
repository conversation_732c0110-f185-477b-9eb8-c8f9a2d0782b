"use client";

import { AwardsPage, useAwards } from "@/features/awards";

export default function AwardsPageRoute() {
  const { data, isLoading, error, retry } = useAwards();

  return (
    <AwardsPage
      data={data || {
        id: "awards",
        pageTitle: "Awards",
        description: "Loading...",
        prizesList: [],
        additionalAwardsList: [],
      }}
      isLoading={isLoading}
      error={error}
      retryAction={retry}
    />
  );
}
