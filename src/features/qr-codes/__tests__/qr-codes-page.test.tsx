import React from "react";
import { render, screen } from "@testing-library/react";
import QRCodesPage from "@/app/(inapp)/qr-codes/page";
import { useAuthContext } from "@/shared/contexts/auth-context";
import type { QRCode } from "../types";

// Mock dependencies
jest.mock("@/shared/contexts/auth-context");
jest.mock("../components/qr-code-form", () => ({
  QRCodeForm: ({ initialQRCodes }: { initialQRCodes: QRCode[] }) => (
    <div data-testid="qr-code-form">
      QR Code Form - Initial QR Codes: {initialQRCodes.length}
    </div>
  ),
}));
jest.mock("../components/qr-code-list", () => ({
  QRCodeList: ({ qrCodes }: { qrCodes: QRCode[] }) => (
    <div data-testid="qr-code-list">
      QR Code List - QR Codes: {qrCodes.length}
    </div>
  ),
}));

const mockUseAuthContext = useAuthContext as jest.MockedFunction<typeof useAuthContext>;

describe("QRCodesPage", () => {
  const mockQRCodes: QRCode[] = [
    {
      id: "qr-1",
      url: "https://example.com",
      qrImage: "data:image/png;base64,mock-image-1",
    },
    {
      id: "qr-2",
      url: "https://test.com",
      qrImage: "data:image/png;base64,mock-image-2",
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render page with QR codes from user context", () => {
    mockUseAuthContext.mockReturnValue({
      userAuth: {
        isAuth: true,
        initialized: true,
        userInfo: {
          id: "user-1",
          qrCodes: mockQRCodes,
        },
      },
    } as any);

    render(<QRCodesPage />);

    expect(screen.getByText("QR Codes")).toBeInTheDocument();
    expect(screen.getByText("Generate and manage QR codes for your links")).toBeInTheDocument();
    
    expect(screen.getByTestId("qr-code-form")).toBeInTheDocument();
    expect(screen.getByText("QR Code Form - Initial QR Codes: 2")).toBeInTheDocument();
    
    expect(screen.getByTestId("qr-code-list")).toBeInTheDocument();
    expect(screen.getByText("QR Code List - QR Codes: 2")).toBeInTheDocument();
  });

  it("should render page with empty QR codes when user has none", () => {
    mockUseAuthContext.mockReturnValue({
      userAuth: {
        isAuth: true,
        initialized: true,
        userInfo: {
          id: "user-1",
          qrCodes: [],
        },
      },
    } as any);

    render(<QRCodesPage />);

    expect(screen.getByText("QR Codes")).toBeInTheDocument();
    expect(screen.getByText("Generate and manage QR codes for your links")).toBeInTheDocument();
    
    expect(screen.getByTestId("qr-code-form")).toBeInTheDocument();
    expect(screen.getByText("QR Code Form - Initial QR Codes: 0")).toBeInTheDocument();
    
    expect(screen.getByTestId("qr-code-list")).toBeInTheDocument();
    expect(screen.getByText("QR Code List - QR Codes: 0")).toBeInTheDocument();
  });

  it("should render page with empty QR codes when qrCodes is undefined", () => {
    mockUseAuthContext.mockReturnValue({
      userAuth: {
        isAuth: true,
        initialized: true,
        userInfo: {
          id: "user-1",
          // qrCodes is undefined
        },
      },
    } as any);

    render(<QRCodesPage />);

    expect(screen.getByText("QR Codes")).toBeInTheDocument();
    expect(screen.getByText("Generate and manage QR codes for your links")).toBeInTheDocument();
    
    expect(screen.getByTestId("qr-code-form")).toBeInTheDocument();
    expect(screen.getByText("QR Code Form - Initial QR Codes: 0")).toBeInTheDocument();
    
    expect(screen.getByTestId("qr-code-list")).toBeInTheDocument();
    expect(screen.getByText("QR Code List - QR Codes: 0")).toBeInTheDocument();
  });

  it("should render page with empty QR codes when userInfo is null", () => {
    mockUseAuthContext.mockReturnValue({
      userAuth: {
        isAuth: true,
        initialized: true,
        userInfo: null,
      },
    } as any);

    render(<QRCodesPage />);

    expect(screen.getByText("QR Codes")).toBeInTheDocument();
    expect(screen.getByText("Generate and manage QR codes for your links")).toBeInTheDocument();
    
    expect(screen.getByTestId("qr-code-form")).toBeInTheDocument();
    expect(screen.getByText("QR Code Form - Initial QR Codes: 0")).toBeInTheDocument();
    
    expect(screen.getByTestId("qr-code-list")).toBeInTheDocument();
    expect(screen.getByText("QR Code List - QR Codes: 0")).toBeInTheDocument();
  });

  it("should have correct page structure and styling", () => {
    mockUseAuthContext.mockReturnValue({
      userAuth: {
        isAuth: true,
        initialized: true,
        userInfo: {
          id: "user-1",
          qrCodes: mockQRCodes,
        },
      },
    } as any);

    render(<QRCodesPage />);

    // Check page title exists
    const title = screen.getByText("QR Codes");
    expect(title).toBeInTheDocument();

    // Check description exists
    const description = screen.getByText("Generate and manage QR codes for your links");
    expect(description).toBeInTheDocument();

    // Check that both components are rendered
    expect(screen.getByTestId("qr-code-form")).toBeInTheDocument();
    expect(screen.getByTestId("qr-code-list")).toBeInTheDocument();
  });

  it("should pass correct props to QRCodeForm", () => {
    mockUseAuthContext.mockReturnValue({
      userAuth: {
        isAuth: true,
        initialized: true,
        userInfo: {
          id: "user-1",
          qrCodes: mockQRCodes,
        },
      },
    } as any);

    render(<QRCodesPage />);

    // The form should receive the QR codes as initial data
    expect(screen.getByText("QR Code Form - Initial QR Codes: 2")).toBeInTheDocument();
  });

  it("should pass correct props to QRCodeList", () => {
    mockUseAuthContext.mockReturnValue({
      userAuth: {
        isAuth: true,
        initialized: true,
        userInfo: {
          id: "user-1",
          qrCodes: mockQRCodes,
        },
      },
    } as any);

    render(<QRCodesPage />);

    // The list should receive the QR codes to display
    expect(screen.getByText("QR Code List - QR Codes: 2")).toBeInTheDocument();
  });

  it("should handle different QR code array lengths", () => {
    const testCases = [
      { qrCodes: [], expectedCount: 0 },
      { qrCodes: [mockQRCodes[0]], expectedCount: 1 },
      { qrCodes: mockQRCodes, expectedCount: 2 },
      { qrCodes: [...mockQRCodes, { id: "qr-3", url: "https://third.com", qrImage: "image3" }], expectedCount: 3 },
    ];

    testCases.forEach(({ qrCodes, expectedCount }) => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
          userInfo: {
            id: "user-1",
            qrCodes,
          },
        },
      } as any);

      const { unmount } = render(<QRCodesPage />);

      expect(screen.getByText(`QR Code Form - Initial QR Codes: ${expectedCount}`)).toBeInTheDocument();
      expect(screen.getByText(`QR Code List - QR Codes: ${expectedCount}`)).toBeInTheDocument();

      unmount();
    });
  });

  it("should render consistently regardless of auth state", () => {
    // Test with different auth states to ensure page renders consistently
    const authStates = [
      {
        userAuth: {
          isAuth: true,
          initialized: true,
          userInfo: { id: "user-1", qrCodes: mockQRCodes },
        },
      },
      {
        userAuth: {
          isAuth: false,
          initialized: true,
          userInfo: null,
        },
      },
      {
        userAuth: {
          isAuth: true,
          initialized: false,
          userInfo: null,
        },
      },
    ];

    authStates.forEach((authState) => {
      mockUseAuthContext.mockReturnValue(authState as any);

      const { unmount } = render(<QRCodesPage />);

      // Page should always render the basic structure
      expect(screen.getByText("QR Codes")).toBeInTheDocument();
      expect(screen.getByText("Generate and manage QR codes for your links")).toBeInTheDocument();
      expect(screen.getByTestId("qr-code-form")).toBeInTheDocument();
      expect(screen.getByTestId("qr-code-list")).toBeInTheDocument();

      unmount();
    });
  });
});
