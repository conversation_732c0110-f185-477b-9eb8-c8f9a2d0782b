import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { QRCodeList } from "../components/qr-code-list";
import { useQRCodes } from "../hooks/use-qr-codes";
import { fetchImageWithProxy } from "@/shared/lib/s3-proxy";
import { saveAs } from "file-saver";
import type { QRCode } from "../types";

// Mock dependencies
jest.mock("../hooks/use-qr-codes");
jest.mock("@/shared/lib/s3-proxy");
jest.mock("file-saver");

const mockUseQRCodes = useQRCodes as jest.MockedFunction<typeof useQRCodes>;
const mockFetchImageWithProxy = fetchImageWithProxy as jest.MockedFunction<typeof fetchImageWithProxy>;
const mockSaveAs = saveAs as jest.MockedFunction<typeof saveAs>;

describe("QRCodeList", () => {
  const mockDeleteQRCode = jest.fn();
  const mockProcessing = {
    isSaving: false,
    isDeleting: false,
    isGenerating: false,
  };

  const mockQRCodes: QRCode[] = [
    {
      id: "qr-1",
      url: "https://example.com",
      qrImage: "https://api.example.com/uploads/qr-1.png",
    },
    {
      id: "qr-2",
      url: "https://test.com/long/path/to/resource?param=value",
      qrImage: "data:image/png;base64,mock-image-2",
    },
    {
      documentId: "doc-3",
      url: "https://another-example.com",
      qrImage: "https://api.example.com/uploads/qr-3.png",
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseQRCodes.mockReturnValue({
      processing: mockProcessing,
      saveQRCode: jest.fn(),
      deleteQRCode: mockDeleteQRCode,
    });
  });

  it("should render empty state when no QR codes provided", () => {
    render(<QRCodeList qrCodes={[]} />);

    expect(screen.getByText("You don't have any QR codes yet.")).toBeInTheDocument();
  });

  it("should render QR codes grid when codes are provided", () => {
    render(<QRCodeList qrCodes={mockQRCodes} />);

    expect(screen.getByText("Your QR Codes")).toBeInTheDocument();
    expect(screen.getAllByRole("img")).toHaveLength(3);
    
    // Check that URLs are displayed
    expect(screen.getByText("https://example.com")).toBeInTheDocument();
    expect(screen.getByText("https://test.com/long/path/to/resource?param=value")).toBeInTheDocument();
    expect(screen.getByText("https://another-example.com")).toBeInTheDocument();
  });

  it("should render QR code images with correct attributes", () => {
    render(<QRCodeList qrCodes={mockQRCodes} />);

    const images = screen.getAllByRole("img");
    
    expect(images[0]).toHaveAttribute("src", "https://api.example.com/uploads/qr-1.png");
    expect(images[0]).toHaveAttribute("alt", "QR Code - https://example.com");
    
    expect(images[1]).toHaveAttribute("src", "data:image/png;base64,mock-image-2");
    expect(images[1]).toHaveAttribute("alt", "QR Code - https://test.com/long/path/to/resource?param=value");
  });

  it("should render download and delete buttons for each QR code", () => {
    render(<QRCodeList qrCodes={mockQRCodes} />);

    const downloadButtons = screen.getAllByRole("button", { name: /download/i });
    // Delete buttons only have icons, so we'll find them by their container or other means
    const allButtons = screen.getAllByRole("button");
    const deleteButtons = allButtons.filter(button =>
      button.querySelector('svg[class*="trash"]') !== null
    );

    expect(downloadButtons).toHaveLength(3);
    expect(deleteButtons).toHaveLength(3);
  });

  it("should handle download with proxy for external URLs", async () => {
    const user = userEvent.setup();
    const mockBlob = new Blob(["mock-image-data"], { type: "image/png" });
    
    mockFetchImageWithProxy.mockResolvedValue(mockBlob);
    
    render(<QRCodeList qrCodes={mockQRCodes} />);

    const downloadButtons = screen.getAllByRole("button", { name: /download/i });
    await user.click(downloadButtons[0]);

    await waitFor(() => {
      expect(mockFetchImageWithProxy).toHaveBeenCalledWith("https://api.example.com/uploads/qr-1.png");
      expect(mockSaveAs).toHaveBeenCalledWith(mockBlob, "qrcode-https___example_com.png");
    });
  });

  it("should handle direct download for data URLs", async () => {
    const user = userEvent.setup();
    
    render(<QRCodeList qrCodes={mockQRCodes} />);

    const downloadButtons = screen.getAllByRole("button", { name: /download/i });
    await user.click(downloadButtons[1]); // Second QR code has data URL

    await waitFor(() => {
      expect(mockFetchImageWithProxy).toHaveBeenCalledWith("data:image/png;base64,mock-image-2");
      expect(mockSaveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        "qrcode-https___test_com_long_path_to_resource_param_value.png"
      );
    });
  });

  it("should handle download error gracefully", async () => {
    const user = userEvent.setup();
    const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    
    mockFetchImageWithProxy.mockRejectedValue(new Error("Download failed"));
    
    render(<QRCodeList qrCodes={mockQRCodes} />);

    const downloadButtons = screen.getAllByRole("button", { name: /download/i });
    await user.click(downloadButtons[0]);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith("Download failed:", expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it("should show delete confirmation dialog when delete button is clicked", async () => {
    const user = userEvent.setup();

    render(<QRCodeList qrCodes={mockQRCodes} />);

    const allButtons = screen.getAllByRole("button");
    const deleteButtons = allButtons.filter(button =>
      button.querySelector('svg[class*="trash"]') !== null
    );
    await user.click(deleteButtons[0]);

    expect(screen.getByText("Delete QR Code")).toBeInTheDocument();
    expect(screen.getByText("Are you sure you want to delete this QR code? This action cannot be undone.")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Delete" })).toBeInTheDocument();
  });

  it("should cancel delete when cancel button is clicked", async () => {
    const user = userEvent.setup();

    render(<QRCodeList qrCodes={mockQRCodes} />);

    const allButtons = screen.getAllByRole("button");
    const deleteButtons = allButtons.filter(button =>
      button.querySelector('svg[class*="trash"]') !== null
    );
    await user.click(deleteButtons[0]);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await user.click(cancelButton);

    expect(screen.queryByText("Delete QR Code")).not.toBeInTheDocument();
    expect(mockDeleteQRCode).not.toHaveBeenCalled();
  });

  it("should delete QR code when confirmed", async () => {
    const user = userEvent.setup();
    const mockOnQRCodeDeleted = jest.fn();

    render(<QRCodeList qrCodes={mockQRCodes} onQRCodeDeleted={mockOnQRCodeDeleted} />);

    const allButtons = screen.getAllByRole("button");
    const deleteButtons = allButtons.filter(button =>
      button.querySelector('svg[class*="trash"]') !== null
    );
    await user.click(deleteButtons[0]);

    const confirmButton = screen.getByRole("button", { name: "Delete" });
    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockDeleteQRCode).toHaveBeenCalledWith("qr-1");
      expect(mockOnQRCodeDeleted).toHaveBeenCalledWith("qr-1");
    });
  });

  it("should handle delete error gracefully", async () => {
    const user = userEvent.setup();
    const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

    mockDeleteQRCode.mockRejectedValue(new Error("Delete failed"));

    render(<QRCodeList qrCodes={mockQRCodes} />);

    const allButtons = screen.getAllByRole("button");
    const deleteButtons = allButtons.filter(button =>
      button.querySelector('svg[class*="trash"]') !== null
    );
    await user.click(deleteButtons[0]);

    const confirmButton = screen.getByRole("button", { name: "Delete" });
    await user.click(confirmButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith("Error deleting QR code:", expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it("should show loading state during delete operation", async () => {
    const user = userEvent.setup();

    mockUseQRCodes.mockReturnValue({
      processing: { ...mockProcessing, isDeleting: true },
      saveQRCode: jest.fn(),
      deleteQRCode: mockDeleteQRCode,
    });

    render(<QRCodeList qrCodes={mockQRCodes} />);

    // During delete operation, there should be a loading indicator
    expect(screen.getByText("Deleting...")).toBeInTheDocument();
    // Check for the loading spinner by its class
    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it("should generate correct filename for download", async () => {
    const user = userEvent.setup();
    const mockBlob = new Blob(["mock-image-data"], { type: "image/png" });
    
    mockFetchImageWithProxy.mockResolvedValue(mockBlob);
    
    const qrCodeWithComplexUrl: QRCode = {
      id: "complex-qr",
      url: "https://example.com/path?param1=value1&param2=value2#section",
      qrImage: "https://api.example.com/uploads/complex-qr.png",
    };
    
    render(<QRCodeList qrCodes={[qrCodeWithComplexUrl]} />);

    const downloadButton = screen.getByRole("button", { name: /download/i });
    await user.click(downloadButton);

    await waitFor(() => {
      expect(mockSaveAs).toHaveBeenCalledWith(
        mockBlob,
        "qrcode-https___example_com_path_param1_value1_param2_value2_section.png"
      );
    });
  });

  it("should handle QR codes with different key types", () => {
    const mixedQRCodes: QRCode[] = [
      { id: "qr-1", url: "https://example1.com", qrImage: "image1.png" },
      { documentId: "doc-2", url: "https://example2.com", qrImage: "image2.png" },
      { url: "https://example3.com", qrImage: "image3.png" }, // No id or documentId
    ];

    render(<QRCodeList qrCodes={mixedQRCodes} />);

    // Should render all QR codes regardless of key type
    expect(screen.getAllByRole("img")).toHaveLength(3);
    expect(screen.getByText("https://example1.com")).toBeInTheDocument();
    expect(screen.getByText("https://example2.com")).toBeInTheDocument();
    expect(screen.getByText("https://example3.com")).toBeInTheDocument();
  });

  it("should truncate long URLs in display", () => {
    const longUrlQRCode: QRCode = {
      id: "long-url-qr",
      url: "https://very-long-domain-name.example.com/very/long/path/to/resource/with/many/segments?param1=very-long-value&param2=another-very-long-value&param3=yet-another-long-value",
      qrImage: "image.png",
    };

    render(<QRCodeList qrCodes={[longUrlQRCode]} />);

    // The URL should be displayed but potentially truncated with CSS
    const urlElement = screen.getByText(longUrlQRCode.url);
    expect(urlElement).toBeInTheDocument();
    expect(urlElement).toHaveClass("line-clamp-2");
  });

  it("should handle missing onQRCodeDeleted callback", async () => {
    const user = userEvent.setup();

    render(<QRCodeList qrCodes={mockQRCodes} />);

    const allButtons = screen.getAllByRole("button");
    const deleteButtons = allButtons.filter(button =>
      button.querySelector('svg[class*="trash"]') !== null
    );
    await user.click(deleteButtons[0]);

    const confirmButton = screen.getByRole("button", { name: "Delete" });
    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockDeleteQRCode).toHaveBeenCalledWith("qr-1");
      // Should not throw error when callback is not provided
    });
  });
});
