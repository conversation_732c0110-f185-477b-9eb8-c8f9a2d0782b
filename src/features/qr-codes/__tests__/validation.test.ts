import { qrCodeFormSchema, type QRCodeFormData } from "../lib/validation";

describe("QR Code Validation", () => {
  describe("qrCodeFormSchema", () => {
    it("should validate a valid URL", () => {
      const validData: QRCodeFormData = {
        url: "https://example.com",
      };

      const result = qrCodeFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.url).toBe("https://example.com");
      }
    });

    it("should validate URLs with different protocols", () => {
      const httpUrl = { url: "http://example.com" };
      const httpsUrl = { url: "https://example.com" };

      expect(qrCodeFormSchema.safeParse(httpUrl).success).toBe(true);
      expect(qrCodeFormSchema.safeParse(httpsUrl).success).toBe(true);
    });

    it("should validate URLs with paths and query parameters", () => {
      const complexUrl = {
        url: "https://example.com/path/to/resource?param1=value1&param2=value2#section",
      };

      const result = qrCodeFormSchema.safeParse(complexUrl);
      expect(result.success).toBe(true);
    });

    it("should validate URLs with subdomains", () => {
      const subdomainUrl = { url: "https://subdomain.example.com" };

      const result = qrCodeFormSchema.safeParse(subdomainUrl);
      expect(result.success).toBe(true);
    });

    it("should reject empty URL", () => {
      const emptyUrl = { url: "" };

      const result = qrCodeFormSchema.safeParse(emptyUrl);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("URL is required");
      }
    });

    it("should reject missing URL field", () => {
      const missingUrl = {};

      const result = qrCodeFormSchema.safeParse(missingUrl);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Required");
      }
    });

    it("should reject invalid URL format", () => {
      const invalidUrls = [
        { url: "not-a-url" },
        { url: "just-text" },
        { url: "example" },
        { url: "www.example" },
      ];

      invalidUrls.forEach((invalidUrl) => {
        const result = qrCodeFormSchema.safeParse(invalidUrl);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(
            "Please enter a valid URL (include <u>https://</u> or <u>http://</u> if missing at the beginning)."
          );
        }
      });
    });

    it("should reject URLs that are too long", () => {
      // Create a URL longer than 2048 characters
      const longPath = "a".repeat(2100);
      const longUrl = { url: `https://example.com/${longPath}` };

      const result = qrCodeFormSchema.safeParse(longUrl);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("URL is too long");
      }
    });

    it("should accept URLs at the maximum length limit", () => {
      // Create a URL exactly at the 2048 character limit
      const pathLength = 2048 - "https://example.com/".length;
      const exactLengthPath = "a".repeat(pathLength);
      const exactLengthUrl = { url: `https://example.com/${exactLengthPath}` };

      const result = qrCodeFormSchema.safeParse(exactLengthUrl);
      expect(result.success).toBe(true);
    });

    it("should validate URLs with special characters", () => {
      const specialCharUrls = [
        { url: "https://example.com/path-with-dashes" },
        { url: "https://example.com/path_with_underscores" },
        { url: "https://example.com/path%20with%20encoded%20spaces" },
        { url: "https://example.com/path?query=value&other=123" },
        { url: "https://example.com/path#anchor" },
      ];

      specialCharUrls.forEach((specialUrl) => {
        const result = qrCodeFormSchema.safeParse(specialUrl);
        expect(result.success).toBe(true);
      });
    });

    it("should validate international domain names", () => {
      const internationalUrls = [
        { url: "https://example.co.uk" },
        { url: "https://example.com.au" },
        { url: "https://example.org" },
        { url: "https://example.net" },
        { url: "https://example.info" },
      ];

      internationalUrls.forEach((intlUrl) => {
        const result = qrCodeFormSchema.safeParse(intlUrl);
        expect(result.success).toBe(true);
      });
    });

    it("should validate localhost URLs", () => {
      const localhostUrls = [
        { url: "http://localhost" },
        { url: "http://localhost:3000" },
        { url: "https://localhost:8080" },
        { url: "http://127.0.0.1" },
        { url: "http://127.0.0.1:3000" },
      ];

      localhostUrls.forEach((localhostUrl) => {
        const result = qrCodeFormSchema.safeParse(localhostUrl);
        expect(result.success).toBe(true);
      });
    });

    it("should validate IP address URLs", () => {
      const ipUrls = [
        { url: "http://***********" },
        { url: "https://********:8080" },
        { url: "http://**********/path" },
      ];

      ipUrls.forEach((ipUrl) => {
        const result = qrCodeFormSchema.safeParse(ipUrl);
        expect(result.success).toBe(true);
      });
    });

    it("should handle whitespace in URLs", () => {
      const whitespaceUrls = [
        { url: "  https://example.com  " },
        { url: "\thttps://example.com\t" },
        { url: "\nhttps://example.com\n" },
      ];

      // Zod's URL validation is more lenient and accepts URLs with whitespace
      whitespaceUrls.forEach((wsUrl) => {
        const result = qrCodeFormSchema.safeParse(wsUrl);
        // These should actually pass because Zod's URL validation is lenient
        expect(result.success).toBe(true);
      });
    });

    it("should provide correct error messages for different validation failures", () => {
      const testCases = [
        {
          input: { url: "" },
          expectedMessage: "URL is required",
        },
        {
          input: { url: "invalid-url" },
          expectedMessage: "Please enter a valid URL (include <u>https://</u> or <u>http://</u> if missing at the beginning).",
        },
        {
          input: { url: `https://example.com/${"a".repeat(2100)}` },
          expectedMessage: "URL is too long",
        },
      ];

      testCases.forEach(({ input, expectedMessage }) => {
        const result = qrCodeFormSchema.safeParse(input);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.issues[0].message).toBe(expectedMessage);
        }
      });
    });
  });
});
