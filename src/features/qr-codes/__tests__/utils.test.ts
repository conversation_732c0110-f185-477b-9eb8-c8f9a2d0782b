import { formatUrl, svgToBlob } from "../lib/utils";

describe("QR Codes Utils", () => {
  describe("formatUrl", () => {
    it("should return empty string for empty input", () => {
      expect(formatUrl("")).toBe("");
    });

    it("should return empty string for null/undefined input", () => {
      expect(formatUrl(null as any)).toBe("");
      expect(formatUrl(undefined as any)).toBe("");
    });

    it("should trim whitespace from URL", () => {
      expect(formatUrl("  example.com  ")).toBe("https://example.com");
    });

    it("should preserve URLs that already have https protocol", () => {
      expect(formatUrl("https://example.com")).toBe("https://example.com");
    });

    it("should preserve URLs that already have http protocol", () => {
      expect(formatUrl("http://example.com")).toBe("http://example.com");
    });

    it("should add https:// to URLs without protocol", () => {
      expect(formatUrl("example.com")).toBe("https://example.com");
      expect(formatUrl("www.example.com")).toBe("https://www.example.com");
      expect(formatUrl("subdomain.example.com")).toBe("https://subdomain.example.com");
    });

    it("should handle URLs with paths", () => {
      expect(formatUrl("example.com/path")).toBe("https://example.com/path");
      expect(formatUrl("https://example.com/path")).toBe("https://example.com/path");
    });

    it("should handle URLs with query parameters", () => {
      expect(formatUrl("example.com?param=value")).toBe("https://example.com?param=value");
      expect(formatUrl("https://example.com?param=value")).toBe("https://example.com?param=value");
    });

    it("should handle complex URLs", () => {
      const complexUrl = "example.com/path/to/resource?param1=value1&param2=value2#section";
      expect(formatUrl(complexUrl)).toBe(`https://${complexUrl}`);
    });
  });

  describe("svgToBlob", () => {
    let mockCanvas: any;
    let mockContext: any;
    let mockImage: any;

    beforeEach(() => {
      // Mock canvas and context
      mockContext = {
        drawImage: jest.fn(),
      };
      
      mockCanvas = {
        width: 0,
        height: 0,
        getContext: jest.fn(() => mockContext),
        toBlob: jest.fn(),
      };

      // Mock document.createElement
      jest.spyOn(document, 'createElement').mockImplementation((tagName) => {
        if (tagName === 'canvas') {
          return mockCanvas as any;
        }
        return document.createElement(tagName);
      });

      // Mock Image constructor
      mockImage = {
        onload: null,
        onerror: null,
        src: '',
        width: 300,
        height: 300,
      };

      global.Image = jest.fn(() => mockImage) as any;

      // Mock XMLSerializer
      global.XMLSerializer = jest.fn(() => ({
        serializeToString: jest.fn(() => '<svg></svg>'),
      })) as any;

      // Mock btoa
      global.btoa = jest.fn((str) => Buffer.from(str).toString('base64'));

      // Mock fetch for image conversion
      global.fetch = jest.fn();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it("should convert SVG to blob successfully", async () => {
      // Create a mock SVG element
      const mockSvgElement = {
        cloneNode: jest.fn(() => ({
          querySelectorAll: jest.fn(() => []),
        })),
      } as any;

      // Mock successful canvas conversion
      mockCanvas.toBlob.mockImplementation((callback: any) => {
        const mockBlob = new Blob(['mock-image-data'], { type: 'image/png' });
        callback(mockBlob);
      });

      const promise = svgToBlob(mockSvgElement);

      // Simulate image load
      setTimeout(() => {
        if (mockImage.onload) {
          mockImage.onload();
        }
      }, 0);

      const result = await promise;

      expect(result).toBeInstanceOf(Blob);
      expect(result.type).toBe('image/png');
      expect(mockSvgElement.cloneNode).toHaveBeenCalledWith(true);
      expect(mockCanvas.toBlob).toHaveBeenCalled();
    });

    it("should handle SVG with images that need conversion", async () => {
      const mockImageElement = {
        getAttribute: jest.fn((attr) => {
          if (attr === 'href') return 'https://example.com/image.png';
          return null;
        }),
        setAttribute: jest.fn(),
        hasAttribute: jest.fn(() => false),
      };

      const mockSvgElement = {
        cloneNode: jest.fn(() => ({
          querySelectorAll: jest.fn(() => [mockImageElement]),
        })),
      } as any;

      // Mock fetch for image conversion
      const mockBlob = new Blob(['mock-image'], { type: 'image/png' });
      (global.fetch as jest.Mock).mockResolvedValue({
        blob: () => Promise.resolve(mockBlob),
      });

      // Mock FileReader
      const mockFileReader = {
        onloadend: null,
        readAsDataURL: jest.fn(),
        result: 'data:image/png;base64,mock-base64-data',
      };

      global.FileReader = jest.fn(() => mockFileReader) as any;

      mockCanvas.toBlob.mockImplementation((callback: any) => {
        const resultBlob = new Blob(['mock-image-data'], { type: 'image/png' });
        callback(resultBlob);
      });

      const promise = svgToBlob(mockSvgElement);

      // Simulate FileReader completion
      setTimeout(() => {
        if (mockFileReader.onloadend) {
          mockFileReader.onloadend();
        }
      }, 0);

      // Simulate image load
      setTimeout(() => {
        if (mockImage.onload) {
          mockImage.onload();
        }
      }, 10);

      const result = await promise;

      expect(result).toBeInstanceOf(Blob);
      expect(global.fetch).toHaveBeenCalledWith('https://example.com/image.png');
      expect(mockImageElement.setAttribute).toHaveBeenCalledWith('href', 'data:image/png;base64,mock-base64-data');
    });

    it("should handle image conversion errors gracefully", async () => {
      const mockImageElement = {
        getAttribute: jest.fn((attr) => {
          if (attr === 'href') return 'https://example.com/invalid-image.png';
          return null;
        }),
        setAttribute: jest.fn(),
        hasAttribute: jest.fn(() => false),
      };

      const mockSvgElement = {
        cloneNode: jest.fn(() => ({
          querySelectorAll: jest.fn(() => [mockImageElement]),
        })),
      } as any;

      // Mock fetch failure
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      mockCanvas.toBlob.mockImplementation((callback: any) => {
        const resultBlob = new Blob(['mock-image-data'], { type: 'image/png' });
        callback(resultBlob);
      });

      const promise = svgToBlob(mockSvgElement);

      // Simulate image load
      setTimeout(() => {
        if (mockImage.onload) {
          mockImage.onload();
        }
      }, 0);

      const result = await promise;

      expect(result).toBeInstanceOf(Blob);
      // Should not have called setAttribute due to fetch error
      expect(mockImageElement.setAttribute).not.toHaveBeenCalled();
    });

    it("should reject when canvas conversion fails", async () => {
      const mockSvgElement = {
        cloneNode: jest.fn(() => ({
          querySelectorAll: jest.fn(() => []),
        })),
      } as any;

      // Mock canvas toBlob failure
      mockCanvas.toBlob.mockImplementation((callback: any) => {
        callback(null);
      });

      const promise = svgToBlob(mockSvgElement);

      // Simulate image load
      setTimeout(() => {
        if (mockImage.onload) {
          mockImage.onload();
        }
      }, 0);

      await expect(promise).rejects.toThrow('Failed to convert SVG to blob');
    });

    it("should reject when image fails to load", async () => {
      const mockSvgElement = {
        cloneNode: jest.fn(() => ({
          querySelectorAll: jest.fn(() => []),
        })),
      } as any;

      const promise = svgToBlob(mockSvgElement);

      // Simulate image error
      setTimeout(() => {
        if (mockImage.onerror) {
          mockImage.onerror();
        }
      }, 0);

      await expect(promise).rejects.toThrow('Failed to load SVG');
    });
  });
});
