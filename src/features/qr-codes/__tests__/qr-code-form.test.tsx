import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { QRCodeForm } from "../components/qr-code-form";
import { useQRCodes } from "../hooks/use-qr-codes";
import type { QRCode } from "../types";

// Mock dependencies
jest.mock("../hooks/use-qr-codes");
jest.mock("qrcode.react", () => ({
  QRCodeSVG: ({ value, ...props }: any) => (
    <svg data-testid="qr-code-svg" data-value={value} {...props}>
      <rect width="300" height="300" fill="white" />
      <text x="150" y="150" textAnchor="middle">QR Code: {value}</text>
    </svg>
  ),
}));

const mockUseQRCodes = useQRCodes as jest.MockedFunction<typeof useQRCodes>;

describe("QRCodeForm", () => {
  const mockSaveQRCode = jest.fn();
  const mockProcessing = {
    isSaving: false,
    isDeleting: false,
    isGenerating: false,
  };

  const mockInitialQRCodes: QRCode[] = [
    {
      id: "qr-1",
      url: "https://example.com",
      qrImage: "data:image/png;base64,mock-image-1",
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseQRCodes.mockReturnValue({
      processing: mockProcessing,
      saveQRCode: mockSaveQRCode,
      deleteQRCode: jest.fn(),
    });
  });

  it("should render form with initial state", () => {
    render(<QRCodeForm />);

    // Use getAllByText to handle multiple elements with same text
    const generateTexts = screen.getAllByText("Generate QR Code");
    expect(generateTexts.length).toBeGreaterThan(0);
    expect(screen.getByLabelText("URL")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("https://example.com")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /generate qr code/i })).toBeInTheDocument();
  });

  it("should render with initial QR codes", () => {
    render(<QRCodeForm initialQRCodes={mockInitialQRCodes} />);

    // Use getAllByText to handle multiple elements with same text
    const generateTexts = screen.getAllByText("Generate QR Code");
    expect(generateTexts.length).toBeGreaterThan(0);
    expect(mockUseQRCodes).toHaveBeenCalledWith(mockInitialQRCodes);
  });

  it("should disable generate button when URL is empty", () => {
    render(<QRCodeForm />);

    const generateButton = screen.getByRole("button", { name: /generate qr code/i });
    expect(generateButton).toBeDisabled();
  });

  it("should enable generate button when URL is entered", async () => {
    const user = userEvent.setup();
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "https://example.com");

    expect(generateButton).toBeEnabled();
  });

  it("should show validation error for invalid URL", async () => {
    const user = userEvent.setup();
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "invalid-url");
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid url/i)).toBeInTheDocument();
    });
  });

  it("should generate QR code when valid URL is submitted", async () => {
    const user = userEvent.setup();
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "https://example.com");
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByTestId("qr-code-svg")).toBeInTheDocument();
      expect(screen.getByTestId("qr-code-svg")).toHaveAttribute("data-value", "https://example.com");
    });
  });

  it("should show save button after QR code is generated", async () => {
    const user = userEvent.setup();
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "https://example.com");
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /save qr code/i })).toBeInTheDocument();
    });
  });

  it("should call saveQRCode when save button is clicked", async () => {
    const user = userEvent.setup();
    const mockOnQRCodeSaved = jest.fn();
    
    render(<QRCodeForm onQRCodeSaved={mockOnQRCodeSaved} />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "https://example.com");
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /save qr code/i })).toBeInTheDocument();
    });

    const mockSavedQRCode: QRCode = {
      id: "new-qr",
      url: "https://example.com",
      qrImage: "data:image/png;base64,mock-saved-image",
    };

    mockSaveQRCode.mockResolvedValue(mockSavedQRCode);

    const saveButton = screen.getByRole("button", { name: /save qr code/i });
    await user.click(saveButton);

    await waitFor(() => {
      expect(mockSaveQRCode).toHaveBeenCalledWith(
        "https://example.com",
        expect.any(SVGElement)
      );
      expect(mockOnQRCodeSaved).toHaveBeenCalledWith(mockSavedQRCode);
    });
  });

  it("should reset form after successful save", async () => {
    const user = userEvent.setup();
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "https://example.com");
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /save qr code/i })).toBeInTheDocument();
    });

    mockSaveQRCode.mockResolvedValue({
      id: "new-qr",
      url: "https://example.com",
      qrImage: "data:image/png;base64,mock-saved-image",
    });

    const saveButton = screen.getByRole("button", { name: /save qr code/i });
    await user.click(saveButton);

    await waitFor(() => {
      expect(urlInput).toHaveValue("");
      expect(screen.queryByTestId("qr-code-svg")).not.toBeInTheDocument();
    });
  });

  it("should show loading state during save operation", async () => {
    const user = userEvent.setup();
    
    mockUseQRCodes.mockReturnValue({
      processing: { ...mockProcessing, isSaving: true },
      saveQRCode: mockSaveQRCode,
      deleteQRCode: jest.fn(),
    });

    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "https://example.com");
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /saving/i })).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /saving/i })).toBeDisabled();
    });
  });

  it("should show generating state during generation", async () => {
    const user = userEvent.setup();
    
    mockUseQRCodes.mockReturnValue({
      processing: { ...mockProcessing, isGenerating: true },
      saveQRCode: mockSaveQRCode,
      deleteQRCode: jest.fn(),
    });

    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");

    await user.type(urlInput, "https://example.com");

    expect(screen.getByRole("button", { name: /generating/i })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /generating/i })).toBeDisabled();
  });

  it("should handle save error gracefully", async () => {
    const user = userEvent.setup();
    const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});
    
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "https://example.com");
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByRole("button", { name: /save qr code/i })).toBeInTheDocument();
    });

    mockSaveQRCode.mockRejectedValue(new Error("Save failed"));

    const saveButton = screen.getByRole("button", { name: /save qr code/i });
    await user.click(saveButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith("Error saving QR code:", expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it("should render QR code with correct properties", async () => {
    const user = userEvent.setup();
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "https://example.com");
    await user.click(generateButton);

    await waitFor(() => {
      const qrCodeSvg = screen.getByTestId("qr-code-svg");
      expect(qrCodeSvg).toHaveAttribute("data-value", "https://example.com");
      // Check that QR code is rendered with expected props
      expect(qrCodeSvg).toBeInTheDocument();
    });
  });

  it("should handle form submission with Enter key", async () => {
    const user = userEvent.setup();
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");

    await user.type(urlInput, "https://example.com");
    await user.keyboard("{Enter}");

    await waitFor(() => {
      expect(screen.getByTestId("qr-code-svg")).toBeInTheDocument();
    });
  });

  it("should validate URL format and show appropriate error message", async () => {
    const user = userEvent.setup();
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    await user.type(urlInput, "not-a-valid-url");
    await user.click(generateButton);

    await waitFor(() => {
      const errorMessage = screen.getByText(/please enter a valid url/i);
      expect(errorMessage).toBeInTheDocument();
      // Check that the error message contains the expected HTML formatting
      expect(errorMessage.innerHTML).toContain('<span class="underline">https://</span>');
    });
  });

  it("should clear validation errors when valid URL is entered", async () => {
    const user = userEvent.setup();
    render(<QRCodeForm />);

    const urlInput = screen.getByLabelText("URL");
    const generateButton = screen.getByRole("button", { name: /generate qr code/i });

    // First enter invalid URL
    await user.type(urlInput, "invalid");
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid url/i)).toBeInTheDocument();
    });

    // Clear and enter valid URL
    await user.clear(urlInput);
    await user.type(urlInput, "https://example.com");
    await user.click(generateButton);

    await waitFor(() => {
      expect(screen.queryByText(/please enter a valid url/i)).not.toBeInTheDocument();
      expect(screen.getByTestId("qr-code-svg")).toBeInTheDocument();
    });
  });
});
