import { renderHook, act, waitFor } from "@testing-library/react";
import { useQRCodes } from "../hooks/use-qr-codes";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { getCookie } from "@/shared/lib/auth";
import { toast } from "sonner";
import { formatUrl, svgToBlob } from "../lib/utils";
import type { QRCode } from "../types";

// Mock dependencies
jest.mock("@/shared/contexts/auth-context");
jest.mock("@/shared/lib/auth");
jest.mock("sonner");
jest.mock("../lib/utils");

const mockUseAuthContext = useAuthContext as jest.MockedFunction<typeof useAuthContext>;
const mockGetCookie = getCookie as jest.MockedFunction<typeof getCookie>;
const mockToast = toast as jest.Mocked<typeof toast>;
const mockFormatUrl = formatUrl as jest.MockedFunction<typeof formatUrl>;
const mockSvgToBlob = svgToBlob as jest.MockedFunction<typeof svgToBlob>;

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe("useQRCodes", () => {
  const mockUpdateUserData = jest.fn();
  const mockInitialQRCodes: QRCode[] = [
    {
      id: "qr-1",
      url: "https://example.com",
      qrImage: "data:image/png;base64,mock-image-1",
    },
    {
      id: "qr-2", 
      url: "https://test.com",
      qrImage: "data:image/png;base64,mock-image-2",
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseAuthContext.mockReturnValue({
      updateUserData: mockUpdateUserData,
      userAuth: {
        isAuth: true,
        initialized: true,
        userInfo: { id: "user-1" },
      },
    } as any);

    mockGetCookie.mockImplementation((name) => {
      if (name === "userId") return "user-1";
      if (name === "jwt") return "mock-jwt-token";
      return null;
    });

    mockFormatUrl.mockImplementation((url) => url.startsWith("http") ? url : `https://${url}`);
    
    // Mock environment variable
    process.env.NEXT_PUBLIC_API_URL = "http://localhost:1339";
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("initialization", () => {
    it("should initialize with correct default processing state", () => {
      const { result } = renderHook(() => useQRCodes());

      expect(result.current.processing.isSaving).toBe(false);
      expect(result.current.processing.isDeleting).toBe(false);
      expect(result.current.processing.isGenerating).toBe(false);
    });

    it("should initialize with provided initial QR codes", () => {
      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      expect(typeof result.current.saveQRCode).toBe("function");
      expect(typeof result.current.deleteQRCode).toBe("function");
    });
  });

  describe("saveQRCode", () => {
    const mockSvgElement = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    const mockBlob = new Blob(["mock-image-data"], { type: "image/png" });

    beforeEach(() => {
      mockSvgToBlob.mockResolvedValue(mockBlob);
      mockFormatUrl.mockReturnValue("https://example.com");
    });

    it("should save QR code successfully", async () => {
      const mockUploadResponse = {
        ok: true,
        json: async () => [{ id: 1, url: "https://api.example.com/uploads/qr-code.png" }],
      };

      const mockUserUpdateResponse = {
        ok: true,
        json: async () => ({
          id: "user-1",
          qrCodes: [
            ...mockInitialQRCodes,
            {
              id: "qr-3",
              url: "https://example.com",
              qrImage: "https://api.example.com/uploads/qr-code.png",
            },
          ],
        }),
      };

      mockFetch
        .mockResolvedValueOnce(mockUploadResponse as Response)
        .mockResolvedValueOnce(mockUserUpdateResponse as Response);

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      let savedQRCode: QRCode;
      await act(async () => {
        savedQRCode = await result.current.saveQRCode("example.com", mockSvgElement);
      });

      expect(mockFormatUrl).toHaveBeenCalledWith("example.com");
      expect(mockSvgToBlob).toHaveBeenCalledWith(mockSvgElement);
      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(mockUpdateUserData).toHaveBeenCalled();
      expect(mockToast.success).toHaveBeenCalledWith("QR Code saved successfully!");
      expect(savedQRCode!).toEqual({
        id: "1",
        documentId: undefined,
        url: "https://example.com",
        qrImage: "https://api.example.com/uploads/qr-code.png",
      });
    });

    it("should handle upload failure", async () => {
      const mockUploadResponse = {
        ok: false,
        status: 500,
        statusText: "Internal Server Error",
      };

      mockFetch.mockResolvedValueOnce(mockUploadResponse as Response);

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      await act(async () => {
        await expect(
          result.current.saveQRCode("example.com", mockSvgElement)
        ).rejects.toThrow("Failed to upload QR code image");
      });

      expect(mockToast.error).toHaveBeenCalledWith("Failed to save QR code. Please try again.");
      expect(result.current.processing.isSaving).toBe(false);
    });

    it("should handle user update failure", async () => {
      const mockUploadResponse = {
        ok: true,
        json: async () => [{ id: 1, url: "https://api.example.com/uploads/qr-code.png" }],
      };

      const mockUserUpdateResponse = {
        ok: false,
        status: 400,
        statusText: "Bad Request",
      };

      mockFetch
        .mockResolvedValueOnce(mockUploadResponse as Response)
        .mockResolvedValueOnce(mockUserUpdateResponse as Response);

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      await act(async () => {
        await expect(
          result.current.saveQRCode("example.com", mockSvgElement)
        ).rejects.toThrow("Failed to update user QR codes");
      });

      expect(mockToast.error).toHaveBeenCalledWith("Failed to save QR code. Please try again.");
    });

    it("should handle missing authentication", async () => {
      mockGetCookie.mockReturnValue(null);

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      await act(async () => {
        await expect(
          result.current.saveQRCode("example.com", mockSvgElement)
        ).rejects.toThrow("Authentication required");
      });

      expect(mockToast.error).toHaveBeenCalledWith("Failed to save QR code. Please try again.");
    });

    it("should handle SVG conversion failure", async () => {
      mockSvgToBlob.mockRejectedValue(new Error("SVG conversion failed"));

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      await act(async () => {
        await expect(
          result.current.saveQRCode("example.com", mockSvgElement)
        ).rejects.toThrow("SVG conversion failed");
      });

      expect(mockToast.error).toHaveBeenCalledWith("Failed to save QR code. Please try again.");
      expect(result.current.processing.isSaving).toBe(false);
    });

    it("should set processing state correctly during save operation", async () => {
      const mockUploadResponse = {
        ok: true,
        json: async () => [{ id: 1, url: "https://api.example.com/uploads/qr-code.png" }],
      };

      const mockUserUpdateResponse = {
        ok: true,
        json: async () => ({ id: "user-1", qrCodes: [] }),
      };

      mockFetch
        .mockResolvedValueOnce(mockUploadResponse as Response)
        .mockResolvedValueOnce(mockUserUpdateResponse as Response);

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      expect(result.current.processing.isSaving).toBe(false);

      // Start the save operation but don't await it immediately
      let savePromise: Promise<any>;
      act(() => {
        savePromise = result.current.saveQRCode("example.com", mockSvgElement);
      });

      // The processing state should be set synchronously
      expect(result.current.processing.isSaving).toBe(true);

      // Now await the promise
      await act(async () => {
        await savePromise;
      });

      // Check that processing state is reset after operation
      expect(result.current.processing.isSaving).toBe(false);
    });
  });

  describe("deleteQRCode", () => {
    it("should delete QR code successfully", async () => {
      const mockUserUpdateResponse = {
        ok: true,
        json: async () => ({
          id: "user-1",
          qrCodes: [mockInitialQRCodes[1]], // Only second QR code remains
        }),
      };

      mockFetch.mockResolvedValueOnce(mockUserUpdateResponse as Response);

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      await act(async () => {
        await result.current.deleteQRCode("qr-1");
      });

      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:1339/users/user-1",
        expect.objectContaining({
          method: "PUT",
          headers: {
            Authorization: "Bearer mock-jwt-token",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ qrCodes: [mockInitialQRCodes[1]] }),
        })
      );

      expect(mockUpdateUserData).toHaveBeenCalled();
      expect(mockToast.success).toHaveBeenCalledWith("QR Code deleted successfully!");
    });

    it("should handle delete failure", async () => {
      const mockUserUpdateResponse = {
        ok: false,
        status: 500,
        statusText: "Internal Server Error",
      };

      mockFetch.mockResolvedValueOnce(mockUserUpdateResponse as Response);

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      await act(async () => {
        await expect(result.current.deleteQRCode("qr-1")).rejects.toThrow(
          "Failed to delete QR code"
        );
      });

      expect(mockToast.error).toHaveBeenCalledWith("Failed to delete QR code. Please try again.");
    });

    it("should handle missing authentication for delete", async () => {
      mockGetCookie.mockReturnValue(null);

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      await act(async () => {
        await expect(result.current.deleteQRCode("qr-1")).rejects.toThrow("Authentication required");
      });

      expect(mockToast.error).toHaveBeenCalledWith("Failed to delete QR code. Please try again.");
    });

    it("should set processing state correctly during delete operation", async () => {
      const mockUserUpdateResponse = {
        ok: true,
        json: async () => ({ id: "user-1", qrCodes: [] }),
      };

      mockFetch.mockResolvedValueOnce(mockUserUpdateResponse as Response);

      const { result } = renderHook(() => useQRCodes(mockInitialQRCodes));

      expect(result.current.processing.isDeleting).toBe(false);

      // Start the delete operation but don't await it immediately
      let deletePromise: Promise<any>;
      act(() => {
        deletePromise = result.current.deleteQRCode("qr-1");
      });

      // The processing state should be set synchronously
      expect(result.current.processing.isDeleting).toBe(true);

      // Now await the promise
      await act(async () => {
        await deletePromise;
      });

      // Check that processing state is reset after operation
      expect(result.current.processing.isDeleting).toBe(false);
    });
  });
});
