// Dashboard widget types
export interface DashboardWidget {
  id: string;
  type: WidgetType;
  title: string;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  data?: any;
}

export type WidgetType = "listing-sheets" | "social-media-posts" | "favorite-assets";

export interface WidgetData {
  "listing-sheets": ListingSheetWidgetData;
  "social-media-posts": SocialMediaWidgetData;
  "favorite-assets": FavoriteAssetsWidgetData;
}

// Listing Sheets Widget
export interface ListingSheetWidgetData {
  sheets: ListingSheetItem[];
  isLoading: boolean;
  error?: string;
}

export interface ListingSheetItem {
  id: string;
  title: string;
  createdAt: string;
  slug: string;
}

// Social Media Posts Widget
export interface SocialMediaWidgetData {
  nextPost: SocialMediaPostItem | null;
  isLoading: boolean;
  error?: string;
}

export interface SocialMediaPostItem {
  id: number;
  month: string | null;
  postDate: string;
  isExtra: boolean;
  extraTitle?: string;
}

// Favorite Assets Widget
export interface FavoriteAssetsWidgetData {
  assets: FavoriteAssetItem[];
  isLoading: boolean;
  error?: string;
}

export interface FavoriteAssetItem {
  id: string;
  documentId: string;
  title: string;
  documentType: "printable" | "compliance" | "resource" | "fintrac";
  url?: string;
}

// Shortcut tiles
export interface ShortcutTile {
  id: string;
  title: string;
  icon: string;
  color: TileColor;
  path: string;
  isExternal?: boolean;
}

export type TileColor = 
  | "gradientred"
  | "gradientyellow" 
  | "gradientgreen"
  | "gradientteal"
  | "gradientblue"
  | "gradientdark"
  | "gradientgrey";

// Widget management
export interface AddWidgetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddWidget: (type: WidgetType) => void;
}

export interface WidgetSelectorProps {
  onSelect: (type: WidgetType) => void;
}

// User favorite assets
export interface UserFavoriteAssets {
  favoriteAssets?: string[]; // Array of document IDs
}

// API responses
export interface FavoriteAssetsResponse {
  data: FavoriteAssetItem[];
  error?: string;
}
