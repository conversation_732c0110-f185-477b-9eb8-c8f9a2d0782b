"use client";

import { CardTile } from "./card-tile";
import { shortcutTiles } from "../lib/shortcut-tiles-data";

/**
 * ShortcutTiles component that displays the dashboard shortcut buttons
 * in a responsive grid layout matching the legacy implementation
 */
export function ShortcutTiles() {
  return (
    <div className="w-full mb-8">
      <h2 className="text-2xl font-bold mb-6 text-slate-900">Dashboard</h2>
      
      {/* Tiles Grid - Responsive layout matching legacy */}
      <div className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-12 gap-4 lg:gap-6">
        {shortcutTiles.map((tile) => (
          <div 
            key={tile.id}
            className="col-span-1 lg:col-span-1"
          >
            <CardTile
              icon={tile.icon}
              title={tile.title}
              color={tile.color}
              path={tile.path}
              isExternal={tile.isExternal}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
