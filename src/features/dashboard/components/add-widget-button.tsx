"use client";

import { Plus } from "lucide-react";
import { But<PERSON> } from "@/shared/ui/button";

interface AddWidgetButtonProps {
  onClick: () => void;
}

/**
 * Button component for adding new widgets to the dashboard
 */
export function AddWidgetButton({ onClick }: AddWidgetButtonProps) {
  return (
    <div className="flex items-center justify-center min-h-[200px] border-2 border-dashed border-muted-foreground/25 rounded-lg hover:border-muted-foreground/50 transition-colors">
      <Button
        variant="ghost"
        onClick={onClick}
        className="flex flex-col items-center space-y-2 h-auto py-4 px-6 text-muted-foreground hover:text-foreground"
      >
        <Plus className="h-8 w-8" />
        <span className="text-sm">Click to Add a Dashboard Widget</span>
      </Button>
    </div>
  );
}
