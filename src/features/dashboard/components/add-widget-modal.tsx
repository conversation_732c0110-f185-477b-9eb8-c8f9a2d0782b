"use client";

import { useState } from "react";
import { X } from "lucide-react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { Button } from "@/shared/ui/button";
import type { WidgetType, AddWidgetModalProps } from "../types";

const widgetOptions = [
  { value: "listing-sheets", label: "Listing Sheets" },
  { value: "social-media-posts", label: "Social Media Post" },
  { value: "favorite-assets", label: "Favorite Assets" },
] as const;

/**
 * Modal for selecting and adding new widgets to the dashboard
 */
export function AddWidgetModal({ isOpen, onClose, onAddWidget }: AddWidgetModalProps) {
  const [selectedWidget, setSelectedWidget] = useState<WidgetType | "">("");

  const handleAddWidget = () => {
    if (selectedWidget) {
      onAddWidget(selectedWidget as WidgetType);
      setSelectedWidget("");
    }
  };

  const handleClose = () => {
    setSelectedWidget("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Select a Widget
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <Select value={selectedWidget} onValueChange={setSelectedWidget}>
            <SelectTrigger>
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {widgetOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleAddWidget}
              disabled={!selectedWidget}
            >
              Add Widget
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
