"use client";

import { useRouter } from "next/navigation";
import { cn } from "@/shared/lib/utils";
import type { TileColor } from "../types";

interface CardTileProps {
  icon: string;
  title: string;
  color: TileColor;
  path: string;
  isExternal?: boolean;
  onClick?: () => void;
}

/**
 * CardTile component that replicates the legacy dashboard shortcut buttons
 * with exact styling, gradients, and navigation functionality
 */
export function CardTile({ 
  icon, 
  title, 
  color, 
  path, 
  isExternal = false,
  onClick 
}: CardTileProps) {
  const router = useRouter();

  const handleClick = () => {
    if (onClick) {
      onClick();
      return;
    }

    if (isExternal) {
      window.open(path, '_blank', 'noopener,noreferrer');
    } else {
      router.push(path);
    }
  };

  return (
    <div 
      className="w-full p-0 block text-center transition-all duration-200 ease-out relative z-50 cursor-pointer hover:scale-105"
      onClick={handleClick}
    >
      <div className={cn(
        "w-full block text-center rounded-xl relative",
        "before:content-[''] before:w-1/2 before:h-1/2 before:block before:bg-black",
        "before:absolute before:top-[33%] before:left-[33%] before:z-10 before:pointer-events-none",
        "before:rounded-full before:mix-blend-overlay before:opacity-35 before:blur-[12px]",
        getGradientClass(color)
      )}>
        <img 
          src={icon} 
          alt={title}
          className="w-full max-w-full h-auto p-6 relative z-20"
        />
      </div>
      <h3 className="font-bold text-sm font-semibold text-slate-900 mt-2">
        {title}
      </h3>
    </div>
  );
}

function getGradientClass(color: TileColor): string {
  switch (color) {
    case "gradientred":
      return "bg-gradient-to-br from-[#c72f20] to-[#ad0707]";
    case "gradientyellow":
      return "bg-gradient-to-br from-[#dcbb00] to-[#c6a143]";
    case "gradientgreen":
      return "bg-gradient-to-br from-[#d3e062] to-[#cddc4c]";
    case "gradientteal":
      return "bg-gradient-to-br from-[#00d2ac] to-[#478482]";
    case "gradientblue":
      return "bg-gradient-to-br from-[#00a0df] to-[#6d9db8]";
    case "gradientdark":
      return "bg-gradient-to-br from-[#bbaed0] to-[#a898c2]";
    case "gradientgrey":
      return "bg-gradient-to-br from-[#fafdff] to-[#dde9ef]";
    default:
      return "bg-gradient-to-br from-gray-200 to-gray-300";
  }
}
