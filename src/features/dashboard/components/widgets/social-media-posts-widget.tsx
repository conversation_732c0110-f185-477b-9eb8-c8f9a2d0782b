"use client";

import { useState, useEffect } from "react";
import { Calendar, Clock, ExternalLink, MessageSquare } from "lucide-react";
import { Button } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Skeleton } from "@/shared/ui/skeleton";
import { DashboardApiClient } from "../../lib/api-client";
import type { SocialMediaPostItem } from "../../types";

/**
 * Widget that displays the next social media post to be published
 */
export function SocialMediaPostsWidget() {
  const [nextPost, setNextPost] = useState<SocialMediaPostItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchNextPost = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await DashboardApiClient.getNextSocialMediaPost();
        
        if (response.error) {
          setError(response.error);
        } else {
          setNextPost(response.data);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch social media post");
      } finally {
        setIsLoading(false);
      }
    };

    fetchNextPost();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getTimeUntilPost = (dateString: string) => {
    const now = new Date();
    const postDate = new Date(dateString);
    const diffMs = postDate.getTime() - now.getTime();
    
    if (diffMs <= 0) return "Now";
    
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''}`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''}`;
    } else {
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    }
  };

  const handleViewPost = () => {
    if (nextPost) {
      window.open(`/social-media/${nextPost.id}`, '_blank');
    }
  };

  const handleViewAllPosts = () => {
    window.open('/social-media', '_blank');
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Skeleton className="h-8 w-8 rounded" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
        <Skeleton className="h-8 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-4">
        <p className="text-sm text-muted-foreground mb-2">Failed to load social media posts</p>
        <p className="text-xs text-destructive">{error}</p>
      </div>
    );
  }

  if (!nextPost) {
    return (
      <div className="text-center py-8">
        <MessageSquare className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground mb-2">No upcoming posts scheduled</p>
        <Button 
          variant="outline" 
          size="sm"
          onClick={handleViewAllPosts}
        >
          View Social Media
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Next Post Info */}
      <div className="p-4 rounded-lg border bg-muted/30">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <MessageSquare className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Next Post</span>
          </div>
          {nextPost.isExtra && (
            <Badge variant="secondary" className="text-xs">
              Extra
            </Badge>
          )}
        </div>
        
        <div className="space-y-2">
          <p className="text-sm font-medium">
            {nextPost.isExtra && nextPost.extraTitle 
              ? nextPost.extraTitle 
              : nextPost.month 
                ? `${nextPost.month} Post`
                : 'Social Media Post'
            }
          </p>
          
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(nextPost.postDate)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{formatTime(nextPost.postDate)}</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between pt-2">
            <Badge variant="outline" className="text-xs">
              In {getTimeUntilPost(nextPost.postDate)}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleViewPost}
              className="h-6 w-6 p-0"
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {/* Action Button */}
      <Button 
        variant="outline" 
        size="sm" 
        className="w-full"
        onClick={handleViewAllPosts}
      >
        View All Posts
      </Button>
    </div>
  );
}
