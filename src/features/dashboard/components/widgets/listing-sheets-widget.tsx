"use client";

import { useState, useEffect } from "react";
import { ExternalLink, FileText, Calendar } from "lucide-react";
import { Button } from "@/shared/ui/button";
import { Skeleton } from "@/shared/ui/skeleton";
import { DashboardApiClient } from "../../lib/api-client";
import type { ListingSheetItem } from "../../types";

/**
 * Widget that displays the last 5 created listing sheets
 */
export function ListingSheetsWidget() {
  const [sheets, setSheets] = useState<ListingSheetItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSheets = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await DashboardApiClient.getRecentListingSheets();
        
        if (response.error) {
          setError(response.error);
        } else {
          setSheets(response.data);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch listing sheets");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSheets();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleViewSheet = (slug: string) => {
    window.open(`/listing-sheet/${slug}`, '_blank');
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3">
            <Skeleton className="h-8 w-8 rounded" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-4">
        <p className="text-sm text-muted-foreground mb-2">Failed to load listing sheets</p>
        <p className="text-xs text-destructive">{error}</p>
      </div>
    );
  }

  if (sheets.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground">No listing sheets found</p>
        <Button 
          variant="outline" 
          size="sm" 
          className="mt-2"
          onClick={() => window.open('/listing-sheet', '_blank')}
        >
          Create Your First Sheet
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {sheets.map((sheet) => (
        <div 
          key={sheet.id}
          className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors"
        >
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <div className="flex-shrink-0">
              <FileText className="h-4 w-4 text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {sheet.title}
              </p>
              <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>{formatDate(sheet.createdAt)}</span>
              </div>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewSheet(sheet.slug)}
            className="flex-shrink-0 h-8 w-8 p-0"
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        </div>
      ))}
      
      {sheets.length === 5 && (
        <div className="text-center pt-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => window.open('/listing-sheet', '_blank')}
          >
            View All Sheets
          </Button>
        </div>
      )}
    </div>
  );
}
