"use client";

import { ExternalLink, FileText, Heart, Star } from "lucide-react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Skeleton } from "@/shared/ui/skeleton";
import { useFavoriteAssets } from "../../hooks/use-favorite-assets";

/**
 * Widget that displays user's favorite documents/assets
 */
export function FavoriteAssetsWidget() {
  const { assets, isLoading, error, removeFromFavorites } = useFavoriteAssets();

  const getDocumentTypeColor = (type: string) => {
    switch (type) {
      case "printable":
        return "bg-blue-100 text-blue-800";
      case "compliance":
        return "bg-green-100 text-green-800";
      case "resource":
        return "bg-purple-100 text-purple-800";
      case "fintrac":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getDocumentTypeIcon = (type: string) => {
    switch (type) {
      case "printable":
        return "📄";
      case "compliance":
        return "✅";
      case "resource":
        return "📚";
      case "fintrac":
        return "🏛️";
      default:
        return "📄";
    }
  };

  const handleViewAsset = (asset: any) => {
    const basePath = `/${asset.documentType}`;
    window.open(`${basePath}/${asset.documentId}`, '_blank');
  };

  const handleRemoveFromFavorites = async (documentId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    await removeFromFavorites(documentId);
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3">
            <Skeleton className="h-8 w-8 rounded" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-4">
        <p className="text-sm text-muted-foreground mb-2">Failed to load favorite assets</p>
        <p className="text-xs text-destructive">{error}</p>
      </div>
    );
  }

  if (assets.length === 0) {
    return (
      <div className="text-center py-8">
        <Heart className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
        <p className="text-sm text-muted-foreground mb-2">No favorite assets yet</p>
        <p className="text-xs text-muted-foreground mb-3">
          Add documents to your favorites from any section
        </p>
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => window.open('/printables', '_blank')}
        >
          Browse Documents
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {assets.slice(0, 8).map((asset) => (
        <div 
          key={asset.id}
          className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer"
          onClick={() => handleViewAsset(asset)}
        >
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <div className="flex-shrink-0 text-lg">
              {getDocumentTypeIcon(asset.documentType)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {asset.title}
              </p>
              <div className="flex items-center space-x-2 mt-1">
                <Badge 
                  variant="secondary" 
                  className={`text-xs ${getDocumentTypeColor(asset.documentType)}`}
                >
                  {asset.documentType}
                </Badge>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-1 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => handleRemoveFromFavorites(asset.documentId, e)}
              className="h-6 w-6 p-0 text-yellow-500 hover:text-yellow-600"
            >
              <Star className="h-3 w-3 fill-current" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleViewAsset(asset);
              }}
              className="h-6 w-6 p-0"
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          </div>
        </div>
      ))}
      
      {assets.length > 8 && (
        <div className="text-center pt-2">
          <p className="text-xs text-muted-foreground">
            Showing 8 of {assets.length} favorites
          </p>
        </div>
      )}
    </div>
  );
}
