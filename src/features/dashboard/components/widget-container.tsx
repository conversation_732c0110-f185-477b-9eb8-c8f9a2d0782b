"use client";

import { X } from "lucide-react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import type { DashboardWidget } from "../types";

interface WidgetContainerProps {
  widget: DashboardWidget;
  onRemove: (id: string) => void;
  children: React.ReactNode;
}

/**
 * Container component for dashboard widgets with remove functionality
 */
export function WidgetContainer({ widget, onRemove, children }: WidgetContainerProps) {
  return (
    <Card className="relative">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {widget.title}
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onRemove(widget.id)}
          className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
        >
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
}
