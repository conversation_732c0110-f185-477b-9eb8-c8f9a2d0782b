"use client";

import { useDashboardWidgets } from "../hooks/use-dashboard-widgets";
import { AddWidgetModal } from "./add-widget-modal";
import { AddWidgetButton } from "./add-widget-button";
import { WidgetContainer } from "./widget-container";
import { ListingSheetsWidget } from "./widgets/listing-sheets-widget";
import { SocialMediaPostsWidget } from "./widgets/social-media-posts-widget";
import { FavoriteAssetsWidget } from "./widgets/favorite-assets-widget";
import type { DashboardWidget } from "../types";

/**
 * Main dashboard widgets component that manages widget layout and interactions
 */
export function DashboardWidgets() {
  const {
    widgets,
    addWidget,
    removeWidget,
    isAddWidgetModalOpen,
    openAddWidgetModal,
    closeAddWidgetModal,
  } = useDashboardWidgets();

  const renderWidget = (widget: DashboardWidget) => {
    switch (widget.type) {
      case "listing-sheets":
        return <ListingSheetsWidget />;
      case "social-media-posts":
        return <SocialMediaPostsWidget />;
      case "favorite-assets":
        return <FavoriteAssetsWidget />;
      default:
        return <div>Unknown widget type</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Widgets Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {widgets.map((widget) => (
          <WidgetContainer
            key={widget.id}
            widget={widget}
            onRemove={removeWidget}
          >
            {renderWidget(widget)}
          </WidgetContainer>
        ))}
        
        {/* Add Widget Button */}
        <AddWidgetButton onClick={openAddWidgetModal} />
      </div>

      {/* Add Widget Modal */}
      <AddWidgetModal
        isOpen={isAddWidgetModalOpen}
        onClose={closeAddWidgetModal}
        onAddWidget={addWidget}
      />
    </div>
  );
}
