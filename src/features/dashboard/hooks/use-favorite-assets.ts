'use client';
import { useState, useEffect, useCallback } from "react";
import { DashboardApiClient } from "../lib/api-client";
import { useFavoriteAssetsManager } from "@/shared/hooks/use-favorite-assets-manager";
import type { FavoriteAssetItem } from "../types";

interface UseFavoriteAssetsResult {
  assets: FavoriteAssetItem[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  addToFavorites: (documentId: string) => Promise<void>;
  removeFromFavorites: (documentId: string) => Promise<void>;
}

/**
 * Hook for managing user's favorite assets in the dashboard widget
 */
export function useFavoriteAssets(): UseFavoriteAssetsResult {
  const [assets, setAssets] = useState<FavoriteAssetItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    addToFavorites: addToFavoritesManager,
    removeFromFavorites: removeFromFavoritesManager
  } = useFavoriteAssetsManager();

  const fetchAssets = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await DashboardApiClient.getFavoriteAssets();

      if (response.error) {
        setError(response.error);
      } else {
        setAssets(response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch favorite assets";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const addToFavorites = useCallback(async (documentId: string) => {
    const success = await addToFavoritesManager(documentId);
    if (success) {
      await fetchAssets(); // Refetch to get updated data
    }
  }, [addToFavoritesManager, fetchAssets]);

  const removeFromFavorites = useCallback(async (documentId: string) => {
    const success = await removeFromFavoritesManager(documentId);
    if (success) {
      await fetchAssets(); // Refetch to get updated data
    }
  }, [removeFromFavoritesManager, fetchAssets]);

  // Initial fetch
  useEffect(() => {
    fetchAssets();
  }, [fetchAssets]);

  return {
    assets,
    isLoading,
    error,
    refetch: fetchAssets,
    addToFavorites,
    removeFromFavorites
  };
}
