"use client";

import { useState, useCallback } from "react";
import type { DashboardWidget, WidgetType } from "../types";

interface UseDashboardWidgetsResult {
  widgets: DashboardWidget[];
  addWidget: (type: WidgetType) => void;
  removeWidget: (id: string) => void;
  updateWidget: (id: string, updates: Partial<DashboardWidget>) => void;
  isAddWidgetModalOpen: boolean;
  openAddWidgetModal: () => void;
  closeAddWidgetModal: () => void;
}

/**
 * Hook for managing dashboard widgets
 */
export function useDashboardWidgets(): UseDashboardWidgetsResult {
  const [widgets, setWidgets] = useState<DashboardWidget[]>([]);
  const [isAddWidgetModalOpen, setIsAddWidgetModalOpen] = useState(false);

  const addWidget = useCallback((type: WidgetType) => {
    const newWidget: DashboardWidget = {
      id: `widget-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      title: getWidgetTitle(type),
      position: {
        x: 0,
        y: widgets.length * 2, // Stack widgets vertically
        w: 4,
        h: 3
      }
    };

    setWidgets(prev => [...prev, newWidget]);
    setIsAddWidgetModalOpen(false);
  }, [widgets.length]);

  const removeWidget = useCallback((id: string) => {
    setWidgets(prev => prev.filter(widget => widget.id !== id));
  }, []);

  const updateWidget = useCallback((id: string, updates: Partial<DashboardWidget>) => {
    setWidgets(prev => prev.map(widget => 
      widget.id === id ? { ...widget, ...updates } : widget
    ));
  }, []);

  const openAddWidgetModal = useCallback(() => {
    setIsAddWidgetModalOpen(true);
  }, []);

  const closeAddWidgetModal = useCallback(() => {
    setIsAddWidgetModalOpen(false);
  }, []);

  return {
    widgets,
    addWidget,
    removeWidget,
    updateWidget,
    isAddWidgetModalOpen,
    openAddWidgetModal,
    closeAddWidgetModal
  };
}

function getWidgetTitle(type: WidgetType): string {
  switch (type) {
    case "listing-sheets":
      return "Listing Sheets";
    case "social-media-posts":
      return "Social Media Posts";
    case "favorite-assets":
      return "Favorite Assets";
    default:
      return "Widget";
  }
}
