import { renderHook, act } from '@testing-library/react';
import { useDashboardWidgets } from '../../hooks/use-dashboard-widgets';

describe('useDashboardWidgets', () => {
  it('initializes with empty widgets array', () => {
    const { result } = renderHook(() => useDashboardWidgets());
    
    expect(result.current.widgets).toEqual([]);
    expect(result.current.isAddWidgetModalOpen).toBe(false);
  });

  it('opens and closes add widget modal', () => {
    const { result } = renderHook(() => useDashboardWidgets());
    
    act(() => {
      result.current.openAddWidgetModal();
    });
    
    expect(result.current.isAddWidgetModalOpen).toBe(true);
    
    act(() => {
      result.current.closeAddWidgetModal();
    });
    
    expect(result.current.isAddWidgetModalOpen).toBe(false);
  });

  it('adds a widget', () => {
    const { result } = renderHook(() => useDashboardWidgets());
    
    act(() => {
      result.current.addWidget('listing-sheets');
    });
    
    expect(result.current.widgets).toHaveLength(1);
    expect(result.current.widgets[0]).toMatchObject({
      type: 'listing-sheets',
      title: 'Listing Sheets',
      position: {
        x: 0,
        y: 0,
        w: 4,
        h: 3
      }
    });
    expect(result.current.widgets[0].id).toBeDefined();
  });

  it('adds multiple widgets with correct positioning', () => {
    const { result } = renderHook(() => useDashboardWidgets());
    
    act(() => {
      result.current.addWidget('listing-sheets');
    });
    
    act(() => {
      result.current.addWidget('social-media-posts');
    });
    
    expect(result.current.widgets).toHaveLength(2);
    expect(result.current.widgets[0].position.y).toBe(0);
    expect(result.current.widgets[1].position.y).toBe(2);
  });

  it('removes a widget', () => {
    const { result } = renderHook(() => useDashboardWidgets());
    
    act(() => {
      result.current.addWidget('listing-sheets');
    });
    
    const widgetId = result.current.widgets[0].id;
    
    act(() => {
      result.current.removeWidget(widgetId);
    });
    
    expect(result.current.widgets).toHaveLength(0);
  });

  it('updates a widget', () => {
    const { result } = renderHook(() => useDashboardWidgets());
    
    act(() => {
      result.current.addWidget('listing-sheets');
    });
    
    const widgetId = result.current.widgets[0].id;
    
    act(() => {
      result.current.updateWidget(widgetId, { title: 'Updated Title' });
    });
    
    expect(result.current.widgets[0].title).toBe('Updated Title');
  });

  it('closes modal when adding widget', () => {
    const { result } = renderHook(() => useDashboardWidgets());
    
    act(() => {
      result.current.openAddWidgetModal();
    });
    
    expect(result.current.isAddWidgetModalOpen).toBe(true);
    
    act(() => {
      result.current.addWidget('listing-sheets');
    });
    
    expect(result.current.isAddWidgetModalOpen).toBe(false);
  });

  it('generates correct titles for different widget types', () => {
    const { result } = renderHook(() => useDashboardWidgets());
    
    act(() => {
      result.current.addWidget('listing-sheets');
      result.current.addWidget('social-media-posts');
      result.current.addWidget('favorite-assets');
    });
    
    expect(result.current.widgets[0].title).toBe('Listing Sheets');
    expect(result.current.widgets[1].title).toBe('Social Media Posts');
    expect(result.current.widgets[2].title).toBe('Favorite Assets');
  });

  it('generates unique IDs for widgets', () => {
    const { result } = renderHook(() => useDashboardWidgets());
    
    act(() => {
      result.current.addWidget('listing-sheets');
      result.current.addWidget('listing-sheets');
    });
    
    expect(result.current.widgets[0].id).not.toBe(result.current.widgets[1].id);
  });
});
