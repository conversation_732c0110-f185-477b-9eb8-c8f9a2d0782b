import { DashboardApiClient } from '../../lib/api-client';
import { apiClient } from '@/shared/lib/api';
import { getCookie } from '@/shared/lib/auth';

// Mock dependencies
jest.mock('@/shared/lib/api');
jest.mock('@/shared/lib/auth');

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;
const mockGetCookie = getCookie as jest.Mock;

describe('DashboardApiClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getFavoriteAssets', () => {
    it('returns empty array when user not authenticated', async () => {
      mockGetCookie.mockReturnValue(null);
      
      const result = await DashboardApiClient.getFavoriteAssets();
      
      expect(result).toEqual({ data: [], error: 'User not authenticated' });
    });

    it('returns empty array when user has no favorite assets', async () => {
      mockGetCookie.mockReturnValue('user123');
      mockApiClient.get.mockResolvedValue({
        success: true,
        data: { data: { favoriteAssets: [] } }
      });
      
      const result = await DashboardApiClient.getFavoriteAssets();
      
      expect(result).toEqual({ data: [] });
    });

    it('fetches and transforms favorite assets', async () => {
      mockGetCookie.mockReturnValue('user123');
      
      // Mock user data response
      mockApiClient.get
        .mockResolvedValueOnce({
          success: true,
          data: { data: { favoriteAssets: ['doc1', 'doc2'] } }
        })
        // Mock document responses
        .mockResolvedValueOnce({
          success: true,
          data: { data: { id: 1, documentId: 'doc1', title: 'Document 1', documentType: 'printable' } }
        })
        .mockResolvedValueOnce({
          success: true,
          data: { data: { id: 2, documentId: 'doc2', title: 'Document 2', documentType: 'compliance' } }
        });
      
      const result = await DashboardApiClient.getFavoriteAssets();
      
      expect(result.data).toHaveLength(2);
      expect(result.data[0]).toMatchObject({
        id: 1,
        documentId: 'doc1',
        title: 'Document 1',
        documentType: 'printable'
      });
    });

    it('handles API errors gracefully', async () => {
      mockGetCookie.mockReturnValue('user123');
      mockApiClient.get.mockRejectedValue(new Error('API Error'));
      
      const result = await DashboardApiClient.getFavoriteAssets();
      
      expect(result.error).toBe('API Error');
      expect(result.data).toEqual([]);
    });
  });

  describe('updateFavoriteAssets', () => {
    it('returns error when user not authenticated', async () => {
      mockGetCookie.mockReturnValue(null);
      
      const result = await DashboardApiClient.updateFavoriteAssets(['doc1']);
      
      expect(result).toEqual({ success: false, error: 'User not authenticated' });
    });

    it('updates favorite assets successfully', async () => {
      mockGetCookie.mockReturnValue('user123');
      mockApiClient.put.mockResolvedValue({ success: true });
      
      const result = await DashboardApiClient.updateFavoriteAssets(['doc1', 'doc2']);
      
      expect(result).toEqual({ success: true });
      expect(mockApiClient.put).toHaveBeenCalledWith('/users/user123', {
        favoriteAssets: ['doc1', 'doc2']
      });
    });

    it('handles update errors', async () => {
      mockGetCookie.mockReturnValue('user123');
      mockApiClient.put.mockResolvedValue({ success: false, error: 'Update failed' });
      
      const result = await DashboardApiClient.updateFavoriteAssets(['doc1']);
      
      expect(result).toEqual({ success: false, error: 'Update failed' });
    });
  });

  describe('getRecentListingSheets', () => {
    it('returns empty array when user not authenticated', async () => {
      mockGetCookie.mockReturnValue(null);
      
      const result = await DashboardApiClient.getRecentListingSheets();
      
      expect(result).toEqual({ data: [], error: 'User not authenticated' });
    });

    it('fetches recent listing sheets', async () => {
      mockGetCookie.mockReturnValue('user123');
      mockApiClient.get.mockResolvedValue({
        success: true,
        data: {
          data: [
            { id: 1, title: 'Sheet 1', createdAt: '2024-01-01', slug: 'sheet-1' },
            { id: 2, title: 'Sheet 2', createdAt: '2024-01-02', slug: 'sheet-2' }
          ]
        }
      });
      
      const result = await DashboardApiClient.getRecentListingSheets();
      
      expect(result.data).toHaveLength(2);
      expect(result.data[0]).toMatchObject({
        id: 1,
        title: 'Sheet 1',
        createdAt: '2024-01-01',
        slug: 'sheet-1'
      });
    });
  });

  describe('getNextSocialMediaPost', () => {
    it('returns null when no upcoming posts', async () => {
      mockApiClient.get.mockResolvedValue({
        success: true,
        data: { data: [] }
      });
      
      const result = await DashboardApiClient.getNextSocialMediaPost();
      
      expect(result.data).toBeNull();
    });

    it('returns next social media post', async () => {
      const futureDate = new Date(Date.now() + 86400000).toISOString(); // Tomorrow
      mockApiClient.get.mockResolvedValue({
        success: true,
        data: {
          data: [{
            id: 1,
            month: 'January',
            postDate: futureDate,
            isExtra: false
          }]
        }
      });
      
      const result = await DashboardApiClient.getNextSocialMediaPost();
      
      expect(result.data).toMatchObject({
        id: 1,
        month: 'January',
        postDate: futureDate,
        isExtra: false
      });
    });
  });
});
