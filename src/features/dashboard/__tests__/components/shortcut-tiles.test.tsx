import { render, screen } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { ShortcutTiles } from '../../components/shortcut-tiles';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

const mockPush = jest.fn();
const mockRouter = {
  push: mockPush,
};

describe('ShortcutTiles', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  it('renders the dashboard title', () => {
    render(<ShortcutTiles />);
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toHaveClass('text-2xl', 'font-bold');
  });

  it('renders all shortcut tiles', () => {
    render(<ShortcutTiles />);
    
    // Check for some key tiles
    expect(screen.getByText('Calendar')).toBeInTheDocument();
    expect(screen.getByText('Events')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('Indi App')).toBeInTheDocument();
    expect(screen.getByText('Marketing')).toBeInTheDocument();
    expect(screen.getByText('Social Media')).toBeInTheDocument();
    expect(screen.getByText('Printables')).toBeInTheDocument();
    expect(screen.getByText('Branding')).toBeInTheDocument();
    expect(screen.getByText('Resources')).toBeInTheDocument();
    expect(screen.getByText('Company Directory')).toBeInTheDocument();
    expect(screen.getByText('Lenders')).toBeInTheDocument();
    expect(screen.getByText('Realtors')).toBeInTheDocument();
    expect(screen.getByText('Listing Sheet')).toBeInTheDocument();
    expect(screen.getByText('Technology')).toBeInTheDocument();
    expect(screen.getByText('Compliance')).toBeInTheDocument();
    expect(screen.getByText('FINTRAC')).toBeInTheDocument();
    expect(screen.getByText('Email Signature')).toBeInTheDocument();
    expect(screen.getByText('Newsletter')).toBeInTheDocument();
    expect(screen.getByText('Group Benefits')).toBeInTheDocument();
    expect(screen.getByText('Awards')).toBeInTheDocument();
  });

  it('has responsive grid layout', () => {
    render(<ShortcutTiles />);
    
    const grid = screen.getByText('Calendar').closest('.grid');
    expect(grid).toHaveClass('grid-cols-3', 'md:grid-cols-6', 'lg:grid-cols-12');
  });

  it('renders tiles with correct icons', () => {
    render(<ShortcutTiles />);
    
    const calendarIcon = screen.getByAltText('Calendar');
    expect(calendarIcon).toHaveAttribute('src', '/images/tile-icon-calendar-3d.png');
    
    const eventsIcon = screen.getByAltText('Events');
    expect(eventsIcon).toHaveAttribute('src', '/images/tile-icon-events-3d.png');
  });
});
