import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AddWidgetModal } from '../../components/add-widget-modal';

// Mock scrollIntoView
Element.prototype.scrollIntoView = jest.fn();

describe('AddWidgetModal', () => {
  const mockOnClose = jest.fn();
  const mockOnAddWidget = jest.fn();

  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onAddWidget: mockOnAddWidget,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(<AddWidgetModal {...defaultProps} />);
    
    expect(screen.getByText('Select a Widget')).toBeInTheDocument();
    expect(screen.getByText('Select')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Add Widget')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<AddWidgetModal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Select a Widget')).not.toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', () => {
    render(<AddWidgetModal {...defaultProps} />);
    
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls onClose when X button is clicked', () => {
    render(<AddWidgetModal {...defaultProps} />);
    
    const closeButton = screen.getByRole('button', { name: '' }); // X button
    fireEvent.click(closeButton);
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('has disabled Add Widget button when no selection', () => {
    render(<AddWidgetModal {...defaultProps} />);
    
    const addButton = screen.getByText('Add Widget');
    expect(addButton).toBeDisabled();
  });

  it('enables Add Widget button when selection is made', async () => {
    render(<AddWidgetModal {...defaultProps} />);

    // Open the select dropdown
    const selectTrigger = screen.getByRole('combobox');
    fireEvent.click(selectTrigger);

    // Select an option
    await waitFor(() => {
      fireEvent.click(screen.getByText('Listing Sheets'));
    });

    const addButton = screen.getByText('Add Widget');
    expect(addButton).not.toBeDisabled();
  });

  it('calls onAddWidget with selected widget type', async () => {
    render(<AddWidgetModal {...defaultProps} />);

    // Open the select dropdown
    const selectTrigger = screen.getByRole('combobox');
    fireEvent.click(selectTrigger);

    // Select an option
    await waitFor(() => {
      fireEvent.click(screen.getByText('Social Media Post'));
    });

    // Click Add Widget
    fireEvent.click(screen.getByText('Add Widget'));

    expect(mockOnAddWidget).toHaveBeenCalledWith('social-media-posts');
  });

  it('shows all widget options in dropdown', async () => {
    render(<AddWidgetModal {...defaultProps} />);

    // Open the select dropdown by clicking the trigger button
    const selectTrigger = screen.getByRole('combobox');
    fireEvent.click(selectTrigger);

    await waitFor(() => {
      expect(screen.getByText('Listing Sheets')).toBeInTheDocument();
      expect(screen.getByText('Social Media Post')).toBeInTheDocument();
      expect(screen.getByText('Favorite Assets')).toBeInTheDocument();
    });
  });


});
