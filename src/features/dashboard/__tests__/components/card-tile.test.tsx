import { render, screen, fireEvent } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { CardTile } from '../../components/card-tile';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock window.open
const mockWindowOpen = jest.fn();
Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
});

const mockPush = jest.fn();
const mockRouter = {
  push: mockPush,
};

describe('CardTile', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
  });

  const defaultProps = {
    icon: '/images/test-icon.png',
    title: 'Test Tile',
    color: 'gradientred' as const,
    path: '/test-path',
  };

  it('renders correctly with required props', () => {
    render(<CardTile {...defaultProps} />);
    
    expect(screen.getByText('Test Tile')).toBeInTheDocument();
    expect(screen.getByAltText('Test Tile')).toBeInTheDocument();
    expect(screen.getByAltText('Test Tile')).toHaveAttribute('src', '/images/test-icon.png');
  });

  it('navigates internally when clicked', () => {
    render(<CardTile {...defaultProps} />);
    
    const tile = screen.getByText('Test Tile').closest('div');
    fireEvent.click(tile!);
    
    expect(mockPush).toHaveBeenCalledWith('/test-path');
    expect(mockWindowOpen).not.toHaveBeenCalled();
  });

  it('opens external link when isExternal is true', () => {
    render(<CardTile {...defaultProps} isExternal={true} />);
    
    const tile = screen.getByText('Test Tile').closest('div');
    fireEvent.click(tile!);
    
    expect(mockWindowOpen).toHaveBeenCalledWith('/test-path', '_blank', 'noopener,noreferrer');
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('calls custom onClick handler when provided', () => {
    const mockOnClick = jest.fn();
    render(<CardTile {...defaultProps} onClick={mockOnClick} />);
    
    const tile = screen.getByText('Test Tile').closest('div');
    fireEvent.click(tile!);
    
    expect(mockOnClick).toHaveBeenCalled();
    expect(mockPush).not.toHaveBeenCalled();
    expect(mockWindowOpen).not.toHaveBeenCalled();
  });

  it('applies correct gradient class for different colors', () => {
    const { rerender } = render(<CardTile {...defaultProps} color="gradientblue" />);
    
    let cardBody = screen.getByAltText('Test Tile').parentElement;
    expect(cardBody).toHaveClass('from-[#00a0df]', 'to-[#6d9db8]');
    
    rerender(<CardTile {...defaultProps} color="gradientgreen" />);
    cardBody = screen.getByAltText('Test Tile').parentElement;
    expect(cardBody).toHaveClass('from-[#d3e062]', 'to-[#cddc4c]');
  });

  it('has hover effect styling', () => {
    render(<CardTile {...defaultProps} />);
    
    const tile = screen.getByText('Test Tile').closest('div');
    expect(tile).toHaveClass('hover:scale-105', 'cursor-pointer');
  });
});
