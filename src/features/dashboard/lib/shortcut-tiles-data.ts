import type { ShortcutTile } from "../types";

/**
 * Dashboard shortcut tiles configuration
 * Replicates the exact tiles from legacy dashboard
 */
export const shortcutTiles: ShortcutTile[] = [
  // Row 1 - Red gradient tiles
  {
    id: "calendar",
    title: "Calendar",
    icon: "/images/tile-icon-calendar-3d.png",
    color: "gradientred",
    path: "/company-calendar"
  },
  {
    id: "events",
    title: "Events",
    icon: "/images/tile-icon-events-3d.png",
    color: "gradientred",
    path: "https://indievents.ca",
    isExternal: true
  },
  {
    id: "notifications",
    title: "Notifications",
    icon: "/images/tile-icon-notifications-3d.png",
    color: "gradientred",
    path: "/notifications"
  },

  // Row 2 - Yellow gradient tiles
  {
    id: "indi-app",
    title: "Indi App",
    icon: "/images/tile-icon-indi-app-3d.png",
    color: "gradientyellow",
    path: "/indi-app"
  },
  {
    id: "marketing",
    title: "Marketing",
    icon: "/images/tile-icon-marketing-3d.png",
    color: "gradientyellow",
    path: "/marketing"
  },
  {
    id: "social-media",
    title: "Social Media",
    icon: "/images/tile-icon-socialmedia-3d.png",
    color: "gradientyellow",
    path: "/social-media"
  },
  {
    id: "custom-shop",
    title: "Custom Shop",
    icon: "/images/tile-icon-custom-shop-3d.png",
    color: "gradientyellow",
    path: "/custom-shop"
  },
  {
    id: "operation-impact",
    title: "Operation Impact",
    icon: "/images/tile-icon-gift-3d.png",
    color: "gradientyellow",
    path: "/client-gift"
  },
  {
    id: "qr-codes",
    title: "QR Codes",
    icon: "/images/tile-icon-qr-code-3d.png",
    color: "gradientyellow",
    path: "/qr-codes"
  },
  {
    id: "printables",
    title: "Printables",
    icon: "/images/tile-icon-printables-3d.png",
    color: "gradientyellow",
    path: "/printables"
  },

  // Row 3 - Green gradient tiles
  {
    id: "payroll",
    title: "Payroll",
    icon: "/images/tile-icon-payroll-3d.png",
    color: "gradientgreen",
    path: "https://portal.scarlettnetwork.com/portal/dashboard",
    isExternal: true
  },
  {
    id: "branding",
    title: "Branding",
    icon: "/images/tile-icon-branding-3d.png",
    color: "gradientgreen",
    path: "/branding"
  },
  {
    id: "resources",
    title: "Resources",
    icon: "/images/tile-icon-resources-3d.png",
    color: "gradientgreen",
    path: "/resources"
  },
  {
    id: "company-directory",
    title: "Company Directory",
    icon: "/images/tile-icon-company-directory-3d.png",
    color: "gradientgreen",
    path: "/company-directory"
  },

  // Row 4 - Teal gradient tiles
  {
    id: "lenders",
    title: "Lenders",
    icon: "/images/tile-icon-lenders-3d.png",
    color: "gradientteal",
    path: "/lenders"
  },
  {
    id: "realtors",
    title: "Realtors",
    icon: "/images/tile-icon-realtors-3d.png",
    color: "gradientteal",
    path: "/realtors"
  },

  // Row 5 - Blue gradient tiles
  {
    id: "listing-sheet",
    title: "Listing Sheet",
    icon: "/images/tile-icon-listingsheet-3d.png",
    color: "gradientblue",
    path: "/listing-sheet"
  },
  {
    id: "my-indi-site",
    title: "My Indi Site",
    icon: "/images/tile-icon-indisites-3d.png",
    color: "gradientblue",
    path: "/indi-sites"
  },
  {
    id: "technology",
    title: "Technology",
    icon: "/images/tile-icon-technology-3d.png",
    color: "gradientblue",
    path: "/technology"
  },
  {
    id: "compliance",
    title: "Compliance",
    icon: "/images/tile-icon-compliance-3d.png",
    color: "gradientblue",
    path: "/compliance"
  },
  {
    id: "fintrac",
    title: "FINTRAC",
    icon: "/images/tile-icon-fintrac-3d.png",
    color: "gradientblue",
    path: "/fintrac"
  },

  // Row 6 - Dark gradient tiles
  {
    id: "indi-academy",
    title: "Indi Academy",
    icon: "/images/tile-icon-academy-3d.png",
    color: "gradientdark",
    path: "https://academy.indimortgage.ca",
    isExternal: true
  },
  {
    id: "email-signature",
    title: "Email Signature",
    icon: "/images/tile-icon-email-signature-3d.png",
    color: "gradientdark",
    path: "/email-signature"
  },
  {
    id: "newsletter",
    title: "Newsletter",
    icon: "/images/tile-icon-newsletter-3d.png",
    color: "gradientdark",
    path: "/newsletter-archive"
  },
  {
    id: "tutorials",
    title: "Tutorials & Videos",
    icon: "/images/tile-icon-tutorials-3d.png",
    color: "gradientdark",
    path: "/tutorials"
  },

  // Row 7 - Grey gradient tiles
  {
    id: "group-benefits",
    title: "Group Benefits",
    icon: "/images/tile-icon-group-benefits-3d.png",
    color: "gradientgrey",
    path: "/group-benefits"
  },
  {
    id: "indi-cares",
    title: "indi Cares",
    icon: "/images/tile-icon-indi-cares-3d.png",
    color: "gradientgrey",
    path: "/indi-cares"
  },
  {
    id: "fit-club",
    title: "Fit Club",
    icon: "/images/tile-icon-fitclub-3d.png",
    color: "gradientgrey",
    path: "/indi-fit-club"
  },
  {
    id: "awards",
    title: "Awards",
    icon: "/images/tile-icon-awards-3d.png",
    color: "gradientgrey",
    path: "/awards"
  }
];
