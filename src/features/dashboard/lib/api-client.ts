import { apiClient } from "@/shared/lib/api";
import { getCookie } from "@/shared/lib/auth";
import type { 
  FavoriteAssetsResponse, 
  FavoriteAssetItem,
  ListingSheetItem,
  SocialMediaPostItem 
} from "../types";

/**
 * API client for dashboard widgets
 */
export class DashboardApiClient {
  /**
   * Get user's favorite assets
   */
  static async getFavoriteAssets(): Promise<FavoriteAssetsResponse> {
    try {
      const userId = getCookie("userId");
      if (!userId) {
        return { data: [], error: "User not authenticated" };
      }

      // First get user's favoriteAssets field
      const userResponse = await apiClient.get<any>(`/users/${userId}?populate=*`);
      
      if (!userResponse.success || !userResponse.data) {
        return { data: [], error: "Failed to fetch user data" };
      }

      const userData = userResponse.data?.data || userResponse.data;
      const favoriteAssetIds = userData.favoriteAssets || [];

      if (!Array.isArray(favoriteAssetIds) || favoriteAssetIds.length === 0) {
        return { data: [] };
      }

      // Get documents by IDs
      const documentsPromises = favoriteAssetIds.map(async (id: string) => {
        try {
          const response = await apiClient.get<any>(`/docs/${id}?populate=*`);
          if (response.success && response.data) {
            const doc = response.data?.data || response.data;
            return {
              id: doc.id,
              documentId: doc.documentId,
              title: doc.title,
              documentType: doc.documentType,
              url: doc.provinceFile?.[0]?.file?.url
            } as FavoriteAssetItem;
          }
          return null;
        } catch (error) {
          console.error(`Error fetching document ${id}:`, error);
          return null;
        }
      });

      const documents = await Promise.all(documentsPromises);
      const validDocuments = documents.filter((doc): doc is FavoriteAssetItem => doc !== null);

      return { data: validDocuments };
    } catch (error) {
      console.error("Error fetching favorite assets:", error);
      return { 
        data: [], 
        error: error instanceof Error ? error.message : "Failed to fetch favorite assets" 
      };
    }
  }

  /**
   * Update user's favorite assets
   */
  static async updateFavoriteAssets(favoriteAssets: string[]): Promise<{ success: boolean; error?: string }> {
    try {
      const userId = getCookie("userId");
      if (!userId) {
        return { success: false, error: "User not authenticated" };
      }

      const response = await apiClient.put<any>(`/users/${userId}`, {
        favoriteAssets
      });

      if (response.success) {
        return { success: true };
      }

      return { success: false, error: response.error || "Failed to update favorite assets" };
    } catch (error) {
      console.error("Error updating favorite assets:", error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : "Failed to update favorite assets" 
      };
    }
  }

  /**
   * Get recent listing sheets for widget
   */
  static async getRecentListingSheets(): Promise<{ data: ListingSheetItem[]; error?: string }> {
    try {
      const userId = getCookie("userId");
      if (!userId) {
        return { data: [], error: "User not authenticated" };
      }

      const queryParams = new URLSearchParams();
      queryParams.append("filters[user][id][$eq]", userId);
      queryParams.append("sort", "createdAt:desc");
      queryParams.append("pagination[limit]", "5");
      queryParams.append("populate", "user");

      const response = await apiClient.get<any>(`/listing-sheets?${queryParams.toString()}`);

      if (response.success && response.data) {
        const sheets = (response.data?.data || []).map((sheet: any) => ({
          id: sheet.id,
          title: sheet.title || `Listing Sheet ${sheet.id}`,
          createdAt: sheet.createdAt,
          slug: sheet.slug
        }));

        return { data: sheets };
      }

      return { data: [], error: response.error || "Failed to fetch listing sheets" };
    } catch (error) {
      console.error("Error fetching recent listing sheets:", error);
      return { 
        data: [], 
        error: error instanceof Error ? error.message : "Failed to fetch listing sheets" 
      };
    }
  }

  /**
   * Get next social media post for widget
   */
  static async getNextSocialMediaPost(): Promise<{ data: SocialMediaPostItem | null; error?: string }> {
    try {
      const now = new Date();
      const queryParams = new URLSearchParams();
      
      // Filter for posts with postDate in the future
      queryParams.append("filters[postDate][$gte]", now.toISOString());
      queryParams.append("sort", "postDate:asc");
      queryParams.append("pagination[limit]", "1");

      const response = await apiClient.get<any>(`/social-medias?${queryParams.toString()}`);

      if (response.success && response.data) {
        const posts = response.data?.data || [];
        if (posts.length > 0) {
          const post = posts[0];
          return {
            data: {
              id: post.id,
              month: post.month,
              postDate: post.postDate,
              isExtra: post.isExtra || false,
              extraTitle: post.extraTitle
            }
          };
        }
      }

      return { data: null };
    } catch (error) {
      console.error("Error fetching next social media post:", error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : "Failed to fetch social media post" 
      };
    }
  }
}
