import React from "react";
import { render, screen } from "@testing-library/react";
import { PartnershipLayout } from "../components/partnership-layout";
import type { Partnership } from "../types";

// Mock dependencies
jest.mock("@/shared/components/document-viewer", () => ({
  DocumentViewer: ({ title, fileUrl, backLabel }: any) => (
    <div data-testid="document-viewer">
      Document Viewer - {title} - {fileUrl} - {backLabel}
    </div>
  ),
}));
jest.mock("@/shared/components/body-text-renderer", () => ({
  BodyTextRenderer: ({ content }: { content: string }) => (
    <div data-testid="body-text-renderer">{content}</div>
  ),
}));

describe("PartnershipLayout", () => {
  const mockPartnership: Partnership = {
    id: 1,
    Title: "Strategic Partnership",
    slug: "strategic-partnership",
    content: "This is a comprehensive partnership program that offers various benefits...",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
    publishedAt: "2024-01-01T00:00:00.000Z",
    documentId: "partnership-1",
    locale: "en",
    banner: {
      id: 1,
      name: "partnership-banner.jpg",
      alternativeText: "Strategic Partnership Banner",
      caption: "Official partnership banner",
      width: 800,
      height: 400,
      formats: {},
      hash: "banner123",
      ext: ".jpg",
      mime: "image/jpeg",
      size: 51200,
      url: "https://example.com/partnership-banner.jpg",
      previewUrl: null,
      provider: "local",
      provider_metadata: null,
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      documentId: "banner-1",
      locale: null,
      publishedAt: "2024-01-01T00:00:00.000Z",
      folderPath: null,
    },
    pdf: {
      id: 1,
      name: "partnership-guide.pdf",
      alternativeText: "Partnership Guide PDF",
      caption: "Comprehensive partnership guide",
      width: null,
      height: null,
      formats: [],
      hash: "pdf123",
      ext: ".pdf",
      mime: "application/pdf",
      size: 1048576,
      url: "https://example.com/partnership-guide.pdf",
      previewUrl: null,
      provider: "local",
      provider_metadata: null,
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      documentId: "pdf-1",
      locale: null,
      publishedAt: "2024-01-01T00:00:00.000Z",
      folderPath: null,
    },
  };

  describe("rendering", () => {
    it("should render partnership layout with all sections", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      expect(screen.getByText("Strategic Partnership")).toBeInTheDocument();
      expect(screen.getByRole("img", { name: /strategic partnership banner/i })).toBeInTheDocument();
      expect(screen.getByTestId("body-text-renderer")).toBeInTheDocument();
      expect(screen.getByText("Video")).toBeInTheDocument();
      expect(screen.getByText("Document")).toBeInTheDocument();
      expect(screen.getByTestId("document-viewer")).toBeInTheDocument();
    });

    it("should render page title correctly", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const title = screen.getByText("Strategic Partnership");
      expect(title).toBeInTheDocument();
      expect(title).toHaveClass("text-3xl", "font-bold", "tracking-tight");
    });

    it("should render banner image with correct attributes", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const bannerImage = screen.getByRole("img", { name: /strategic partnership banner/i });
      expect(bannerImage).toBeInTheDocument();
      expect(bannerImage).toHaveAttribute("src", "https://example.com/partnership-banner.jpg");
      expect(bannerImage).toHaveAttribute("alt", "Strategic Partnership Banner");
      expect(bannerImage).toHaveClass("w-full", "h-auto", "rounded-lg", "shadow-sm");
    });

    it("should use title as alt text when banner alternativeText is empty", () => {
      const partnershipWithoutBannerAlt: Partnership = {
        ...mockPartnership,
        banner: {
          ...mockPartnership.banner!,
          alternativeText: "",
        },
      };

      render(<PartnershipLayout partnership={partnershipWithoutBannerAlt} />);

      const bannerImage = screen.getByRole("img");
      expect(bannerImage).toHaveAttribute("alt", "Strategic Partnership");
    });

    it("should render content with BodyTextRenderer", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const bodyTextRenderer = screen.getByTestId("body-text-renderer");
      expect(bodyTextRenderer).toBeInTheDocument();
      expect(bodyTextRenderer).toHaveTextContent("This is a comprehensive partnership program that offers various benefits...");
    });

    it("should render video section with iframe", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      expect(screen.getByText("Video")).toBeInTheDocument();
      
      const iframe = screen.getByTitle("Strategic Partnership Video");
      expect(iframe).toBeInTheDocument();
      expect(iframe).toHaveAttribute("src", "https://www.youtube.com/embed/dQw4w9WgXcQ");
      expect(iframe).toHaveClass("w-full", "h-full", "rounded-lg");
      expect(iframe).toHaveAttribute("frameBorder", "0");
      expect(iframe).toHaveAttribute("allowFullScreen");
    });

    it("should render document section with DocumentViewer", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      expect(screen.getByText("Document")).toBeInTheDocument();
      
      const documentViewer = screen.getByTestId("document-viewer");
      expect(documentViewer).toBeInTheDocument();
      expect(documentViewer).toHaveTextContent("Document Viewer - partnership-guide.pdf - https://example.com/partnership-guide.pdf - Back to Partners");
    });
  });

  describe("video URL conversion", () => {
    it("should convert YouTube watch URL to embed URL", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const iframe = screen.getByTitle("Strategic Partnership Video");
      expect(iframe).toHaveAttribute("src", "https://www.youtube.com/embed/dQw4w9WgXcQ");
    });

    it("should convert YouTube short URL to embed URL", () => {
      const partnershipWithShortUrl: Partnership = {
        ...mockPartnership,
        videoUrl: "https://youtu.be/dQw4w9WgXcQ",
      };

      render(<PartnershipLayout partnership={partnershipWithShortUrl} />);

      const iframe = screen.getByTitle("Strategic Partnership Video");
      expect(iframe).toHaveAttribute("src", "https://www.youtube.com/embed/dQw4w9WgXcQ");
    });

    it("should convert YouTube URL with additional parameters", () => {
      const partnershipWithParams: Partnership = {
        ...mockPartnership,
        videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s",
      };

      render(<PartnershipLayout partnership={partnershipWithParams} />);

      const iframe = screen.getByTitle("Strategic Partnership Video");
      expect(iframe).toHaveAttribute("src", "https://www.youtube.com/embed/dQw4w9WgXcQ");
    });

    it("should convert Vimeo URL to embed URL", () => {
      const partnershipWithVimeo: Partnership = {
        ...mockPartnership,
        videoUrl: "https://vimeo.com/123456789",
      };

      render(<PartnershipLayout partnership={partnershipWithVimeo} />);

      const iframe = screen.getByTitle("Strategic Partnership Video");
      expect(iframe).toHaveAttribute("src", "https://player.vimeo.com/video/123456789");
    });

    it("should return original URL for unrecognized video services", () => {
      const partnershipWithOtherUrl: Partnership = {
        ...mockPartnership,
        videoUrl: "https://example.com/video.mp4",
      };

      render(<PartnershipLayout partnership={partnershipWithOtherUrl} />);

      const iframe = screen.getByTitle("Strategic Partnership Video");
      expect(iframe).toHaveAttribute("src", "https://example.com/video.mp4");
    });
  });

  describe("conditional rendering", () => {
    it("should not render banner section when banner is null", () => {
      const partnershipWithoutBanner: Partnership = {
        ...mockPartnership,
        banner: null,
      };

      render(<PartnershipLayout partnership={partnershipWithoutBanner} />);

      expect(screen.queryByRole("img")).not.toBeInTheDocument();
    });

    it("should not render banner section when banner is undefined", () => {
      const partnershipWithoutBanner: Partnership = {
        ...mockPartnership,
        banner: undefined,
      };

      render(<PartnershipLayout partnership={partnershipWithoutBanner} />);

      expect(screen.queryByRole("img")).not.toBeInTheDocument();
    });

    it("should not render content section when content is empty", () => {
      const partnershipWithoutContent: Partnership = {
        ...mockPartnership,
        content: "",
      };

      render(<PartnershipLayout partnership={partnershipWithoutContent} />);

      expect(screen.queryByTestId("body-text-renderer")).not.toBeInTheDocument();
    });

    it("should not render content section when content is null", () => {
      const partnershipWithoutContent: Partnership = {
        ...mockPartnership,
        content: null as any,
      };

      render(<PartnershipLayout partnership={partnershipWithoutContent} />);

      expect(screen.queryByTestId("body-text-renderer")).not.toBeInTheDocument();
    });

    it("should not render video section when videoUrl is null", () => {
      const partnershipWithoutVideo: Partnership = {
        ...mockPartnership,
        videoUrl: null,
      };

      render(<PartnershipLayout partnership={partnershipWithoutVideo} />);

      expect(screen.queryByText("Video")).not.toBeInTheDocument();
      expect(screen.queryByTitle(/video/i)).not.toBeInTheDocument();
    });

    it("should not render video section when videoUrl is undefined", () => {
      const partnershipWithoutVideo: Partnership = {
        ...mockPartnership,
        videoUrl: undefined,
      };

      render(<PartnershipLayout partnership={partnershipWithoutVideo} />);

      expect(screen.queryByText("Video")).not.toBeInTheDocument();
      expect(screen.queryByTitle(/video/i)).not.toBeInTheDocument();
    });

    it("should not render document section when pdf is null", () => {
      const partnershipWithoutPdf: Partnership = {
        ...mockPartnership,
        pdf: null,
      };

      render(<PartnershipLayout partnership={partnershipWithoutPdf} />);

      expect(screen.queryByText("Document")).not.toBeInTheDocument();
      expect(screen.queryByTestId("document-viewer")).not.toBeInTheDocument();
    });

    it("should not render document section when pdf is undefined", () => {
      const partnershipWithoutPdf: Partnership = {
        ...mockPartnership,
        pdf: undefined,
      };

      render(<PartnershipLayout partnership={partnershipWithoutPdf} />);

      expect(screen.queryByText("Document")).not.toBeInTheDocument();
      expect(screen.queryByTestId("document-viewer")).not.toBeInTheDocument();
    });

    it("should render no content message when all optional content is missing", () => {
      const minimalPartnership: Partnership = {
        ...mockPartnership,
        content: "",
        videoUrl: null,
        banner: null,
        pdf: null,
      };

      render(<PartnershipLayout partnership={minimalPartnership} />);

      expect(screen.getByText("No additional content available for this partnership.")).toBeInTheDocument();
    });

    it("should not render no content message when at least one content section exists", () => {
      const partnershipWithOnlyContent: Partnership = {
        ...mockPartnership,
        videoUrl: null,
        banner: null,
        pdf: null,
      };

      render(<PartnershipLayout partnership={partnershipWithOnlyContent} />);

      expect(screen.queryByText("No additional content available for this partnership.")).not.toBeInTheDocument();
    });
  });

  describe("layout and styling", () => {
    it("should have correct container styling", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      // Check that the page renders with correct structure
      expect(screen.getByText("Strategic Partnership")).toBeInTheDocument();
      // The container classes may vary, so just check that the page renders correctly
      const mainContainer = screen.getByText("Strategic Partnership").closest("div");
      expect(mainContainer).toBeInTheDocument();
    });

    it("should have correct card styling for content sections", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      // Content card
      const contentCard = screen.getByTestId("body-text-renderer").closest("[class*='card']");
      expect(contentCard).toBeInTheDocument();

      // Video card
      const videoCard = screen.getByText("Video").closest("[class*='card']");
      expect(videoCard).toBeInTheDocument();

      // Document card
      const documentCard = screen.getByText("Document").closest("[class*='card']");
      expect(documentCard).toBeInTheDocument();
    });

    it("should have correct prose styling for content", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const proseContainer = screen.getByTestId("body-text-renderer").closest(".prose");
      expect(proseContainer).toHaveClass("prose", "prose-gray", "max-w-none");
    });

    it("should have correct aspect ratio for video", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const videoContainer = screen.getByTitle("Strategic Partnership Video").closest(".aspect-video");
      expect(videoContainer).toBeInTheDocument();
      expect(videoContainer).toHaveClass("w-full");
    });

    it("should have correct section headings styling", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const videoHeading = screen.getByText("Video");
      expect(videoHeading).toHaveClass("text-xl", "font-semibold", "mb-4");

      const documentHeading = screen.getByText("Document");
      expect(documentHeading).toHaveClass("text-xl", "font-semibold", "mb-4");
    });

    it("should center no content message", () => {
      const minimalPartnership: Partnership = {
        ...mockPartnership,
        content: "",
        videoUrl: null,
        banner: null,
        pdf: null,
      };

      render(<PartnershipLayout partnership={minimalPartnership} />);

      const noContentMessage = screen.getByText("No additional content available for this partnership.");
      expect(noContentMessage).toHaveClass("text-muted-foreground");
      expect(noContentMessage.closest(".text-center")).toBeInTheDocument();
    });
  });

  describe("accessibility", () => {
    it("should have proper heading hierarchy", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const mainHeading = screen.getByRole("heading", { level: 1 });
      expect(mainHeading).toHaveTextContent("Strategic Partnership");

      const videoHeading = screen.getByRole("heading", { level: 2, name: "Video" });
      expect(videoHeading).toBeInTheDocument();

      const documentHeading = screen.getByRole("heading", { level: 2, name: "Document" });
      expect(documentHeading).toBeInTheDocument();
    });

    it("should have proper iframe attributes for accessibility", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const iframe = screen.getByTitle("Strategic Partnership Video");
      expect(iframe).toHaveAttribute("title", "Strategic Partnership Video");
      expect(iframe).toHaveAttribute("allow", "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture");
    });

    it("should have proper image alt text", () => {
      render(<PartnershipLayout partnership={mockPartnership} />);

      const bannerImage = screen.getByRole("img");
      expect(bannerImage).toHaveAttribute("alt", "Strategic Partnership Banner");
    });
  });
});
