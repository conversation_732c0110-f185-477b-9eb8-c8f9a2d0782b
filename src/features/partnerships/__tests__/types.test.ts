import type { PartnershipBanner, PartnershipPDF, Partnership, PartnershipsApiResponse, PartnershipPageProps } from "../types";

describe("Partnerships Types", () => {
  describe("PartnershipBanner", () => {
    it("should have correct structure", () => {
      const mockBanner: PartnershipBanner = {
        id: 1,
        name: "partnership-banner.jpg",
        alternativeText: "Partnership Banner",
        caption: "Official partnership banner",
        width: 800,
        height: 400,
        formats: {
          squared: {
            ext: ".jpg",
            url: "https://example.com/squared.jpg",
            hash: "squared123",
            mime: "image/jpeg",
            name: "squared_partnership-banner.jpg",
            path: null,
            size: 15360,
            width: 400,
            height: 400,
          },
          thumbnail: {
            ext: ".jpg",
            url: "https://example.com/thumbnail.jpg",
            hash: "thumb123",
            mime: "image/jpeg",
            name: "thumbnail_partnership-banner.jpg",
            path: "/uploads",
            size: 5120,
            width: 150,
            height: 75,
          },
        },
        hash: "banner123def456",
        ext: ".jpg",
        mime: "image/jpeg",
        size: 51200,
        url: "https://example.com/partnership-banner.jpg",
        previewUrl: "https://example.com/preview.jpg",
        provider: "cloudinary",
        provider_metadata: { public_id: "banner_123" },
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        documentId: "banner-1",
        locale: "en",
        publishedAt: "2024-01-01T00:00:00.000Z",
        folderPath: "/banners",
      };

      expect(mockBanner.id).toBe(1);
      expect(mockBanner.name).toBe("partnership-banner.jpg");
      expect(mockBanner.alternativeText).toBe("Partnership Banner");
      expect(mockBanner.width).toBe(800);
      expect(mockBanner.height).toBe(400);
      expect(mockBanner.formats.squared?.url).toBe("https://example.com/squared.jpg");
      expect(mockBanner.formats.thumbnail?.url).toBe("https://example.com/thumbnail.jpg");
      expect(mockBanner.url).toBe("https://example.com/partnership-banner.jpg");
    });

    it("should allow optional format properties", () => {
      const mockBanner: PartnershipBanner = {
        id: 2,
        name: "simple-banner.png",
        alternativeText: "Simple Banner",
        caption: "",
        width: 600,
        height: 300,
        formats: {},
        hash: "simple456ghi789",
        ext: ".png",
        mime: "image/png",
        size: 25600,
        url: "https://example.com/simple-banner.png",
        previewUrl: null,
        provider: "local",
        provider_metadata: null,
        createdAt: "2024-01-02T00:00:00.000Z",
        updatedAt: "2024-01-02T00:00:00.000Z",
        documentId: "banner-2",
        locale: null,
        publishedAt: "2024-01-02T00:00:00.000Z",
        folderPath: null,
      };

      expect(mockBanner.formats).toEqual({});
      expect(mockBanner.previewUrl).toBeNull();
      expect(mockBanner.provider_metadata).toBeNull();
      expect(mockBanner.locale).toBeNull();
      expect(mockBanner.folderPath).toBeNull();
    });
  });

  describe("PartnershipPDF", () => {
    it("should have correct structure", () => {
      const mockPDF: PartnershipPDF = {
        id: 1,
        name: "partnership-guide.pdf",
        alternativeText: "Partnership Guide PDF",
        caption: "Comprehensive partnership guide",
        width: null,
        height: null,
        formats: [],
        hash: "pdf123abc456",
        ext: ".pdf",
        mime: "application/pdf",
        size: 1048576,
        url: "https://example.com/partnership-guide.pdf",
        previewUrl: "https://example.com/pdf-preview.jpg",
        provider: "aws-s3",
        provider_metadata: { bucket: "documents" },
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        documentId: "pdf-1",
        locale: "en",
        publishedAt: "2024-01-01T00:00:00.000Z",
        folderPath: "/documents",
      };

      expect(mockPDF.id).toBe(1);
      expect(mockPDF.name).toBe("partnership-guide.pdf");
      expect(mockPDF.alternativeText).toBe("Partnership Guide PDF");
      expect(mockPDF.width).toBeNull();
      expect(mockPDF.height).toBeNull();
      expect(mockPDF.formats).toEqual([]);
      expect(mockPDF.ext).toBe(".pdf");
      expect(mockPDF.mime).toBe("application/pdf");
      expect(mockPDF.size).toBe(1048576);
      expect(mockPDF.url).toBe("https://example.com/partnership-guide.pdf");
    });

    it("should allow null optional properties", () => {
      const mockPDF: PartnershipPDF = {
        id: 2,
        name: "simple-doc.docx",
        alternativeText: "",
        caption: "",
        width: 210,
        height: 297,
        formats: [],
        hash: "doc789xyz012",
        ext: ".docx",
        mime: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        size: 524288,
        url: "https://example.com/simple-doc.docx",
        previewUrl: null,
        provider: "local",
        provider_metadata: null,
        createdAt: "2024-01-02T00:00:00.000Z",
        updatedAt: "2024-01-02T00:00:00.000Z",
        documentId: "pdf-2",
        locale: null,
        publishedAt: "2024-01-02T00:00:00.000Z",
        folderPath: null,
      };

      expect(mockPDF.width).toBe(210);
      expect(mockPDF.height).toBe(297);
      expect(mockPDF.previewUrl).toBeNull();
      expect(mockPDF.provider_metadata).toBeNull();
      expect(mockPDF.locale).toBeNull();
      expect(mockPDF.folderPath).toBeNull();
    });
  });

  describe("Partnership", () => {
    it("should have correct structure", () => {
      const mockPartnership: Partnership = {
        id: 1,
        Title: "Strategic Partnership",
        slug: "strategic-partnership",
        content: "This is a comprehensive partnership program...",
        videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        publishedAt: "2024-01-01T00:00:00.000Z",
        documentId: "partnership-1",
        locale: "en",
        banner: {
          id: 1,
          name: "banner.jpg",
          alternativeText: "Partnership Banner",
          caption: "",
          width: 800,
          height: 400,
          formats: {},
          hash: "banner123",
          ext: ".jpg",
          mime: "image/jpeg",
          size: 51200,
          url: "https://example.com/banner.jpg",
          previewUrl: null,
          provider: "local",
          provider_metadata: null,
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          documentId: "banner-1",
          locale: null,
          publishedAt: "2024-01-01T00:00:00.000Z",
          folderPath: null,
        },
        pdf: {
          id: 1,
          name: "guide.pdf",
          alternativeText: "Partnership Guide",
          caption: "",
          width: null,
          height: null,
          formats: [],
          hash: "pdf123",
          ext: ".pdf",
          mime: "application/pdf",
          size: 1048576,
          url: "https://example.com/guide.pdf",
          previewUrl: null,
          provider: "local",
          provider_metadata: null,
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          documentId: "pdf-1",
          locale: null,
          publishedAt: "2024-01-01T00:00:00.000Z",
          folderPath: null,
        },
      };

      expect(mockPartnership.id).toBe(1);
      expect(mockPartnership.Title).toBe("Strategic Partnership");
      expect(mockPartnership.slug).toBe("strategic-partnership");
      expect(mockPartnership.content).toBe("This is a comprehensive partnership program...");
      expect(mockPartnership.videoUrl).toBe("https://www.youtube.com/watch?v=dQw4w9WgXcQ");
      expect(mockPartnership.banner?.name).toBe("banner.jpg");
      expect(mockPartnership.pdf?.name).toBe("guide.pdf");
    });

    it("should allow optional properties", () => {
      const mockPartnership: Partnership = {
        id: 2,
        Title: "Simple Partnership",
        slug: "simple-partnership",
        content: "",
        videoUrl: null,
        createdAt: "2024-01-02T00:00:00.000Z",
        updatedAt: "2024-01-02T00:00:00.000Z",
        publishedAt: null,
        documentId: "partnership-2",
        locale: null,
        banner: null,
        pdf: null,
      };

      expect(mockPartnership.content).toBe("");
      expect(mockPartnership.videoUrl).toBeNull();
      expect(mockPartnership.publishedAt).toBeNull();
      expect(mockPartnership.locale).toBeNull();
      expect(mockPartnership.banner).toBeNull();
      expect(mockPartnership.pdf).toBeNull();
    });

    it("should allow undefined optional properties", () => {
      const mockPartnership: Partnership = {
        id: 3,
        Title: "Minimal Partnership",
        slug: "minimal-partnership",
        content: "Basic content",
        createdAt: "2024-01-03T00:00:00.000Z",
        updatedAt: "2024-01-03T00:00:00.000Z",
        publishedAt: "2024-01-03T00:00:00.000Z",
        documentId: "partnership-3",
        locale: "en",
      };

      expect(mockPartnership.videoUrl).toBeUndefined();
      expect(mockPartnership.banner).toBeUndefined();
      expect(mockPartnership.pdf).toBeUndefined();
    });
  });

  describe("PartnershipsApiResponse", () => {
    it("should have correct structure", () => {
      const mockResponse: PartnershipsApiResponse = {
        data: [
          {
            id: 1,
            Title: "Partnership One",
            slug: "partnership-one",
            content: "Content for partnership one",
            createdAt: "2024-01-01T00:00:00.000Z",
            updatedAt: "2024-01-01T00:00:00.000Z",
            publishedAt: "2024-01-01T00:00:00.000Z",
            documentId: "partnership-1",
            locale: "en",
          },
        ],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 1,
          },
        },
      };

      expect(mockResponse.data).toHaveLength(1);
      expect(mockResponse.data[0].Title).toBe("Partnership One");
      expect(mockResponse.meta).toBeDefined();
      expect(mockResponse.meta.pagination).toBeDefined();
    });

    it("should allow empty data array", () => {
      const mockResponse: PartnershipsApiResponse = {
        data: [],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 0,
            total: 0,
          },
        },
      };

      expect(mockResponse.data).toHaveLength(0);
      expect(mockResponse.meta.pagination.total).toBe(0);
    });

    it("should allow empty meta object", () => {
      const mockResponse: PartnershipsApiResponse = {
        data: [
          {
            id: 1,
            Title: "Test Partnership",
            slug: "test-partnership",
            content: "Test content",
            createdAt: "2024-01-01T00:00:00.000Z",
            updatedAt: "2024-01-01T00:00:00.000Z",
            publishedAt: "2024-01-01T00:00:00.000Z",
            documentId: "partnership-1",
            locale: "en",
          },
        ],
        meta: {},
      };

      expect(mockResponse.data).toHaveLength(1);
      expect(mockResponse.meta).toEqual({});
    });
  });

  describe("PartnershipPageProps", () => {
    it("should have correct structure", () => {
      const mockProps: PartnershipPageProps = {
        params: Promise.resolve({
          slug: "test-partnership",
        }),
      };

      expect(mockProps.params).toBeInstanceOf(Promise);
    });

    it("should resolve to correct params structure", async () => {
      const mockProps: PartnershipPageProps = {
        params: Promise.resolve({
          slug: "strategic-partnership",
        }),
      };

      const resolvedParams = await mockProps.params;
      expect(resolvedParams.slug).toBe("strategic-partnership");
    });
  });
});
