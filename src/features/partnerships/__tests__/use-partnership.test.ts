import { renderHook, waitFor } from "@testing-library/react";
import { usePartnership } from "../hooks/use-partnership";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { getAuthHeaders } from "@/shared/lib/auth";
import type { PartnershipsApiResponse, Partnership } from "../types";

// Mock dependencies
jest.mock("@/shared/contexts/auth-context");
jest.mock("@/shared/lib/auth");

const mockUseAuthContext = useAuthContext as jest.MockedFunction<typeof useAuthContext>;
const mockGetAuthHeaders = getAuthHeaders as jest.MockedFunction<typeof getAuthHeaders>;

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe("usePartnership", () => {
  const mockPartnership: Partnership = {
    id: 1,
    Title: "Strategic Partnership",
    slug: "strategic-partnership",
    content: "This is a comprehensive partnership program...",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
    publishedAt: "2024-01-01T00:00:00.000Z",
    documentId: "partnership-1",
    locale: "en",
    banner: {
      id: 1,
      name: "banner.jpg",
      alternativeText: "Partnership Banner",
      caption: "",
      width: 800,
      height: 400,
      formats: {},
      hash: "banner123",
      ext: ".jpg",
      mime: "image/jpeg",
      size: 51200,
      url: "https://example.com/banner.jpg",
      previewUrl: null,
      provider: "local",
      provider_metadata: null,
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      documentId: "banner-1",
      locale: null,
      publishedAt: "2024-01-01T00:00:00.000Z",
      folderPath: null,
    },
    pdf: {
      id: 1,
      name: "guide.pdf",
      alternativeText: "Partnership Guide",
      caption: "",
      width: null,
      height: null,
      formats: [],
      hash: "pdf123",
      ext: ".pdf",
      mime: "application/pdf",
      size: 1048576,
      url: "https://example.com/guide.pdf",
      previewUrl: null,
      provider: "local",
      provider_metadata: null,
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      documentId: "pdf-1",
      locale: null,
      publishedAt: "2024-01-01T00:00:00.000Z",
      folderPath: null,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockGetAuthHeaders.mockReturnValue({
      "Authorization": "Bearer mock-token",
      "Content-Type": "application/json",
    });

    // Mock environment variable
    process.env.NEXT_PUBLIC_API_URL = "http://localhost:1339";
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("initialization", () => {
    it("should initialize with correct default state", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: false,
          initialized: false,
        },
      } as any);

      const { result } = renderHook(() => usePartnership("test-slug"));

      expect(result.current.partnership).toBeNull();
      expect(result.current.isLoading).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it("should not fetch data when user is not authenticated", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: false,
          initialized: true,
        },
      } as any);

      renderHook(() => usePartnership("test-slug"));

      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should not fetch data when auth is not initialized", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: false,
        },
      } as any);

      renderHook(() => usePartnership("test-slug"));

      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should not fetch data when slug is empty", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      renderHook(() => usePartnership(""));

      expect(mockFetch).not.toHaveBeenCalled();
    });
  });

  describe("successful data fetching", () => {
    it("should fetch and set partnership successfully", async () => {
      const mockResponse: PartnershipsApiResponse = {
        data: [mockPartnership],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 1,
          },
        },
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const { result } = renderHook(() => usePartnership("strategic-partnership"));

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.partnership).toEqual(mockPartnership);
      expect(result.current.error).toBeNull();
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:1339/one-pages?filters[slug][$eq]=strategic-partnership&populate=*",
        {
          headers: {
            "Authorization": "Bearer mock-token",
            "Content-Type": "application/json",
          },
          cache: 'no-store',
        }
      );
    });

    it("should handle partnership not found", async () => {
      const mockResponse: PartnershipsApiResponse = {
        data: [],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 0,
            total: 0,
          },
        },
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const { result } = renderHook(() => usePartnership("non-existent-slug"));

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.partnership).toBeNull();
      expect(result.current.error).toBeNull();
    });

    it("should handle null data response", async () => {
      const mockResponse: PartnershipsApiResponse = {
        data: null as any,
        meta: {},
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const { result } = renderHook(() => usePartnership("test-slug"));

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.partnership).toBeNull();
      expect(result.current.error).toBeNull();
    });
  });

  describe("error handling", () => {
    it("should handle HTTP error responses", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: "Not Found",
      } as Response);

      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

      const { result } = renderHook(() => usePartnership("test-slug"));

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.partnership).toBeNull();
      expect(result.current.error).toBe("Failed to fetch partnership data");
      expect(consoleSpy).toHaveBeenCalledWith("Error fetching partnership:", expect.any(Error));

      consoleSpy.mockRestore();
    });

    it("should handle network errors", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      const networkError = new Error("Network error");
      mockFetch.mockRejectedValueOnce(networkError);

      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

      const { result } = renderHook(() => usePartnership("test-slug"));

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.partnership).toBeNull();
      expect(result.current.error).toBe("Network error");
      expect(consoleSpy).toHaveBeenCalledWith("Error fetching partnership:", networkError);

      consoleSpy.mockRestore();
    });

    it("should handle non-Error exceptions", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockRejectedValueOnce("String error");

      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

      const { result } = renderHook(() => usePartnership("test-slug"));

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.partnership).toBeNull();
      expect(result.current.error).toBe("An error occurred");
      expect(consoleSpy).toHaveBeenCalledWith("Error fetching partnership:", "String error");

      consoleSpy.mockRestore();
    });
  });

  describe("slug changes", () => {
    it("should refetch data when slug changes", async () => {
      const mockResponse1: PartnershipsApiResponse = {
        data: [mockPartnership],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 } },
      };

      const mockPartnership2: Partnership = {
        ...mockPartnership,
        id: 2,
        Title: "Technology Partnership",
        slug: "technology-partnership",
        documentId: "partnership-2",
      };

      const mockResponse2: PartnershipsApiResponse = {
        data: [mockPartnership2],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 } },
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockResponse1,
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockResponse2,
        } as Response);

      const { result, rerender } = renderHook(
        ({ slug }) => usePartnership(slug),
        { initialProps: { slug: "strategic-partnership" } }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.partnership).toEqual(mockPartnership);

      // Change slug
      rerender({ slug: "technology-partnership" });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.partnership).toEqual(mockPartnership2);
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it("should not fetch when slug changes to empty string", async () => {
      const mockResponse: PartnershipsApiResponse = {
        data: [mockPartnership],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 } },
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const { result, rerender } = renderHook(
        ({ slug }) => usePartnership(slug),
        { initialProps: { slug: "strategic-partnership" } }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.partnership).toEqual(mockPartnership);

      // Change slug to empty string
      rerender({ slug: "" });

      // Should not make another fetch call
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });

  describe("auth state changes", () => {
    it("should refetch data when auth state changes from unauthenticated to authenticated", async () => {
      const mockResponse: PartnershipsApiResponse = {
        data: [mockPartnership],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 } },
      };

      // Start with unauthenticated state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: false,
          initialized: true,
        },
      } as any);

      const { result, rerender } = renderHook(() => usePartnership("strategic-partnership"));

      // Initially should not fetch when unauthenticated
      expect(mockFetch).not.toHaveBeenCalled();

      // Change to authenticated state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      rerender();

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result.current.partnership).toEqual(mockPartnership);
    });

    it("should refetch data when initialization state changes", async () => {
      const mockResponse: PartnershipsApiResponse = {
        data: [mockPartnership],
        meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 } },
      };

      // Start with uninitialized state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: false,
        },
      } as any);

      const { result, rerender } = renderHook(() => usePartnership("strategic-partnership"));

      // Initially should not fetch when uninitialized
      expect(mockFetch).not.toHaveBeenCalled();

      // Change to initialized state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      rerender();

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result.current.partnership).toEqual(mockPartnership);
    });
  });

  describe("loading states", () => {
    it("should set loading to true when starting fetch", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      // Create a promise that we can control
      let resolvePromise: (value: any) => void;
      const fetchPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      mockFetch.mockReturnValueOnce(fetchPromise as any);

      const { result } = renderHook(() => usePartnership("test-slug"));

      // Initially loading should be true
      expect(result.current.isLoading).toBe(true);

      // Resolve the promise
      resolvePromise!({
        ok: true,
        json: async () => ({ data: [mockPartnership], meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 } } }),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });
    });

    it("should reset error state when starting new fetch", async () => {
      // Start with authenticated state that will fail
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      // First request fails
      mockFetch.mockRejectedValueOnce(new Error("First error"));

      const { result, unmount } = renderHook(() => usePartnership("test-slug"));

      await waitFor(() => {
        expect(result.current.error).toBe("First error");
      });

      // Unmount and remount with successful response
      unmount();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: [mockPartnership], meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 } } }),
      } as Response);

      const { result: newResult } = renderHook(() => usePartnership("test-slug"));

      await waitFor(() => {
        expect(newResult.current.error).toBeNull();
        expect(newResult.current.partnership).toEqual(mockPartnership);
      });
    });
  });
});
