import { renderHook } from '@testing-library/react';
import { useAwards } from '../hooks/use-awards';
import { apiClient } from '@/shared/lib/api';

// Mock the API client
jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('useAwards', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with loading state', () => {
    const { result } = renderHook(() => useAwards());

    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(null);
  });

  it('should have retry function', () => {
    const { result } = renderHook(() => useAwards());

    expect(typeof result.current.retry).toBe('function');
  });

  it('should call retry function without error', () => {
    const { result } = renderHook(() => useAwards());

    expect(() => result.current.retry()).not.toThrow();
  });

  it('should handle API error gracefully', async () => {
    mockApiClient.get.mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useAwards());

    // Should not crash when API fails
    expect(result.current.isLoading).toBe(true);
  });
});
