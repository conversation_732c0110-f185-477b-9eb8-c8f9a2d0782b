"use client";

import React from "react";
import { AwardsLayout, AwardsPageData } from "@/shared/components";

export interface AwardsPageProps {
  data: AwardsPageData;
  isLoading?: boolean;
  error?: string | null;
  className?: string;
  retryAction?: () => void;
}

const AwardsPage: React.FC<AwardsPageProps> = ({
  data,
  isLoading = false,
  error = null,
  className,
  retryAction,
}) => {
  return (
    <AwardsLayout
      data={data}
      isLoading={isLoading}
      error={error}
      className={className}
      retryAction={retryAction}
    />
  );
};

export default AwardsPage;
