"use client";

import { useState, useEffect } from "react";
import { apiClient } from "@/shared/lib/api";
import { AwardsPageData } from "@/shared/components";


export const useAwards = () => {
  const [data, setData] = useState<AwardsPageData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAwardsData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch from the page-awards endpoint
      const response = await apiClient.get("/page-awards");

      if (response.data) {
        const apiData = (response.data as any).data;

        console.log('Awards API Data:', apiData);

        // Map API data to AwardsPageData interface
        const mappedData: AwardsPageData = {
          id: apiData.id.toString(),
          pageTitle: apiData.pageTitle,
          description: apiData.description,
          prizesList: (apiData.prizesList || []).map((prize: any) => ({
            id: prize.id.toString(),
            title: prize.title,
            description: prize.description,
            amount: prize.amount,
            thumbnail: prize.thumbnail ? {
              url: prize.thumbnail.url,
              alt: prize.thumbnail.alternativeText || prize.title
            } : undefined
          })),
          additionalAwardsList: (apiData.additionalAwardsList || []).map((award: any) => ({
            id: award.id.toString(),
            title: award.title,
            description: award.description
          }))
        };

        setData(mappedData);
      } else {
        setError("Awards data not found");
      }
    } catch (err) {
      console.error("Error fetching awards data:", err);
      setError(err instanceof Error ? err.message : "Failed to load awards data");
    } finally {
      setIsLoading(false);
    }
  };

  const retry = () => {
    fetchAwardsData();
  };

  useEffect(() => {
    fetchAwardsData();
  }, []);

  return {
    data,
    isLoading,
    error,
    retry,
  };
};
