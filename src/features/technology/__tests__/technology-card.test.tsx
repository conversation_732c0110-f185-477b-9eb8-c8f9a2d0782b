import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { TechnologyCard } from "../components/technology-card";
import type { Technology } from "../types";

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: jest.fn(),
});

describe("TechnologyCard", () => {
  const mockTechnology: Technology = {
    id: 1,
    title: "React",
    link: "https://reactjs.org",
    description: "A JavaScript library for building user interfaces",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
    publishedAt: "2024-01-01T00:00:00.000Z",
    documentId: "tech-1",
    logo: {
      id: 1,
      name: "react-logo.png",
      alternativeText: "React Logo",
      caption: "React library logo",
      width: 200,
      height: 200,
      formats: {
        small: {
          ext: ".png",
          url: "https://example.com/react-small.png",
          hash: "react123",
          mime: "image/png",
          name: "small_react-logo.png",
          path: null,
          size: 8192,
          width: 100,
          height: 100,
        },
        medium: {
          ext: ".png",
          url: "https://example.com/react-medium.png",
          hash: "react456",
          mime: "image/png",
          name: "medium_react-logo.png",
          path: null,
          size: 16384,
          width: 200,
          height: 200,
        },
        thumbnail: {
          ext: ".png",
          url: "https://example.com/react-thumb.png",
          hash: "react789",
          mime: "image/png",
          name: "thumb_react-logo.png",
          path: null,
          size: 4096,
          width: 50,
          height: 50,
        },
      },
      hash: "react456def789",
      ext: ".png",
      mime: "image/png",
      size: 32768,
      url: "https://example.com/react-logo.png",
      previewUrl: null,
      provider: "local",
      provider_metadata: null,
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      documentId: "logo-react",
      publishedAt: "2024-01-01T00:00:00.000Z",
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("rendering", () => {
    it("should render technology card with correct information", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      expect(screen.getByText("React")).toBeInTheDocument();
      expect(screen.getByText("A JavaScript library for building user interfaces")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /go to/i })).toBeInTheDocument();
    });

    it("should render logo image with correct attributes", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const logoImage = screen.getByRole("img");
      expect(logoImage).toBeInTheDocument();
      expect(logoImage).toHaveAttribute("src", "https://example.com/react-small.png");
      expect(logoImage).toHaveAttribute("alt", "React Logo");
    });

    it("should use title as alt text when alternativeText is empty", () => {
      const technologyWithoutAlt: Technology = {
        ...mockTechnology,
        logo: {
          ...mockTechnology.logo,
          alternativeText: "",
        },
      };

      render(<TechnologyCard technology={technologyWithoutAlt} />);

      const logoImage = screen.getByRole("img");
      expect(logoImage).toHaveAttribute("alt", "React");
    });

    it("should render description when provided", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      expect(screen.getByText("A JavaScript library for building user interfaces")).toBeInTheDocument();
    });

    it("should not render description when empty", () => {
      const technologyWithoutDescription: Technology = {
        ...mockTechnology,
        description: "",
      };

      render(<TechnologyCard technology={technologyWithoutDescription} />);

      expect(screen.queryByText("A JavaScript library for building user interfaces")).not.toBeInTheDocument();
    });

    it("should render Go To button with external link icon", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const button = screen.getByRole("button", { name: /go to/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass("w-full");
      
      // Check for external link icon (SVG)
      const icon = button.querySelector('svg');
      expect(icon).toBeInTheDocument();
      expect(icon).toHaveClass("h-4", "w-4", "mr-2");
    });
  });

  describe("logo URL selection", () => {
    it("should prefer small format when available", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const logoImage = screen.getByRole("img");
      expect(logoImage).toHaveAttribute("src", "https://example.com/react-small.png");
    });

    it("should fallback to medium format when small is not available", () => {
      const technologyWithoutSmall: Technology = {
        ...mockTechnology,
        logo: {
          ...mockTechnology.logo,
          formats: {
            medium: mockTechnology.logo.formats.medium!,
            thumbnail: mockTechnology.logo.formats.thumbnail!,
          },
        },
      };

      render(<TechnologyCard technology={technologyWithoutSmall} />);

      const logoImage = screen.getByRole("img");
      expect(logoImage).toHaveAttribute("src", "https://example.com/react-medium.png");
    });

    it("should fallback to thumbnail format when small and medium are not available", () => {
      const technologyWithoutSmallMedium: Technology = {
        ...mockTechnology,
        logo: {
          ...mockTechnology.logo,
          formats: {
            thumbnail: mockTechnology.logo.formats.thumbnail!,
          },
        },
      };

      render(<TechnologyCard technology={technologyWithoutSmallMedium} />);

      const logoImage = screen.getByRole("img");
      expect(logoImage).toHaveAttribute("src", "https://example.com/react-thumb.png");
    });

    it("should fallback to original URL when no formats are available", () => {
      const technologyWithoutFormats: Technology = {
        ...mockTechnology,
        logo: {
          ...mockTechnology.logo,
          formats: {},
        },
      };

      render(<TechnologyCard technology={technologyWithoutFormats} />);

      const logoImage = screen.getByRole("img");
      expect(logoImage).toHaveAttribute("src", "https://example.com/react-logo.png");
    });
  });

  describe("URL formatting", () => {
    it("should not modify URLs that already have https protocol", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const button = screen.getByRole("button", { name: /go to/i });
      fireEvent.click(button);

      expect(window.open).toHaveBeenCalledWith("https://reactjs.org", "_blank", "noopener,noreferrer");
    });

    it("should not modify URLs that already have http protocol", () => {
      const technologyWithHttp: Technology = {
        ...mockTechnology,
        link: "http://example.com",
      };

      render(<TechnologyCard technology={technologyWithHttp} />);

      const button = screen.getByRole("button", { name: /go to/i });
      fireEvent.click(button);

      expect(window.open).toHaveBeenCalledWith("http://example.com", "_blank", "noopener,noreferrer");
    });

    it("should add https:// to URLs without protocol", () => {
      const technologyWithoutProtocol: Technology = {
        ...mockTechnology,
        link: "reactjs.org",
      };

      render(<TechnologyCard technology={technologyWithoutProtocol} />);

      const button = screen.getByRole("button", { name: /go to/i });
      fireEvent.click(button);

      expect(window.open).toHaveBeenCalledWith("https://reactjs.org", "_blank", "noopener,noreferrer");
    });

    it("should handle URLs with whitespace", () => {
      const technologyWithWhitespace: Technology = {
        ...mockTechnology,
        link: "  reactjs.org  ",
      };

      render(<TechnologyCard technology={technologyWithWhitespace} />);

      const button = screen.getByRole("button", { name: /go to/i });
      fireEvent.click(button);

      expect(window.open).toHaveBeenCalledWith("https://reactjs.org", "_blank", "noopener,noreferrer");
    });

    it("should handle empty URLs", () => {
      const technologyWithEmptyLink: Technology = {
        ...mockTechnology,
        link: "",
      };

      render(<TechnologyCard technology={technologyWithEmptyLink} />);

      const button = screen.getByRole("button", { name: /go to/i });
      fireEvent.click(button);

      expect(window.open).toHaveBeenCalledWith("", "_blank", "noopener,noreferrer");
    });
  });

  describe("user interactions", () => {
    it("should open link in new tab when Go To button is clicked", async () => {
      const user = userEvent.setup();

      render(<TechnologyCard technology={mockTechnology} />);

      const button = screen.getByRole("button", { name: /go to/i });
      await user.click(button);

      expect(window.open).toHaveBeenCalledWith("https://reactjs.org", "_blank", "noopener,noreferrer");
    });

    it("should handle keyboard navigation", async () => {
      const user = userEvent.setup();

      render(<TechnologyCard technology={mockTechnology} />);

      const button = screen.getByRole("button", { name: /go to/i });
      
      // Focus the button and press Enter
      button.focus();
      await user.keyboard("{Enter}");

      expect(window.open).toHaveBeenCalledWith("https://reactjs.org", "_blank", "noopener,noreferrer");
    });

    it("should handle space key press", async () => {
      const user = userEvent.setup();

      render(<TechnologyCard technology={mockTechnology} />);

      const button = screen.getByRole("button", { name: /go to/i });
      
      // Focus the button and press Space
      button.focus();
      await user.keyboard(" ");

      expect(window.open).toHaveBeenCalledWith("https://reactjs.org", "_blank", "noopener,noreferrer");
    });
  });

  describe("image error handling", () => {
    it("should fallback to original URL when formatted URL fails to load", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const logoImage = screen.getByRole("img");
      
      // Initially should use small format
      expect(logoImage).toHaveAttribute("src", "https://example.com/react-small.png");

      // Simulate image load error
      fireEvent.error(logoImage);

      // Should fallback to original URL
      expect(logoImage).toHaveAttribute("src", "https://example.com/react-logo.png");
    });

    it("should not change src if already using original URL", () => {
      const technologyWithoutFormats: Technology = {
        ...mockTechnology,
        logo: {
          ...mockTechnology.logo,
          formats: {},
        },
      };

      render(<TechnologyCard technology={technologyWithoutFormats} />);

      const logoImage = screen.getByRole("img");
      
      // Should already be using original URL
      expect(logoImage).toHaveAttribute("src", "https://example.com/react-logo.png");

      // Simulate image load error
      fireEvent.error(logoImage);

      // Should remain the same
      expect(logoImage).toHaveAttribute("src", "https://example.com/react-logo.png");
    });
  });

  describe("styling and layout", () => {
    it("should apply hover effects to card", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const card = screen.getByText("React").closest("[class*='card']");
      expect(card).toHaveClass("hover:shadow-md", "transition-shadow");
    });

    it("should have correct card structure", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      // Check for card components
      expect(screen.getByText("React").closest("[class*='card']")).toBeInTheDocument();
    });

    it("should have full height layout", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const card = screen.getByText("React").closest("[class*='card']");
      expect(card).toHaveClass("h-full");
    });

    it("should center logo content", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const logoContainer = screen.getByRole("img").closest(".flex");
      expect(logoContainer).toHaveClass("justify-center");
    });

    it("should apply text truncation to description", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const description = screen.getByText("A JavaScript library for building user interfaces");
      expect(description).toHaveClass("line-clamp-3");
    });
  });

  describe("accessibility", () => {
    it("should have accessible button with proper text", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const button = screen.getByRole("button", { name: /go to/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent("Go To");
    });

    it("should have proper image alt text", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const logoImage = screen.getByRole("img");
      expect(logoImage).toHaveAttribute("alt", "React Logo");
    });

    it("should be keyboard accessible", () => {
      render(<TechnologyCard technology={mockTechnology} />);

      const button = screen.getByRole("button", { name: /go to/i });
      expect(button).toBeVisible();
      
      // Button should be focusable
      button.focus();
      expect(button).toHaveFocus();
    });
  });
});
