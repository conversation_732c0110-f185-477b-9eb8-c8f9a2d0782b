import type { TechnologyLogo, Technology, TechnologiesApiResponse } from "../types";

describe("Technology Types", () => {
  describe("TechnologyLogo", () => {
    it("should have correct structure", () => {
      const mockLogo: TechnologyLogo = {
        id: 1,
        name: "company-logo.png",
        alternativeText: "Company Logo",
        caption: "Official company logo",
        width: 200,
        height: 100,
        formats: {
          thumbnail: {
            ext: ".png",
            url: "https://example.com/thumbnail.png",
            hash: "thumb123",
            mime: "image/png",
            name: "thumbnail_company-logo.png",
            path: null,
            size: 5120,
            width: 50,
            height: 25,
            provider_metadata: {
              public_id: "thumb_logo",
              resource_type: "image",
            },
          },
          small: {
            ext: ".png",
            url: "https://example.com/small.png",
            hash: "small123",
            mime: "image/png",
            name: "small_company-logo.png",
            path: null,
            size: 10240,
            width: 100,
            height: 50,
            provider_metadata: {
              public_id: "small_logo",
              resource_type: "image",
            },
          },
        },
        hash: "abc123def456",
        ext: ".png",
        mime: "image/png",
        size: 20480,
        url: "https://example.com/company-logo.png",
        previewUrl: "https://example.com/preview.png",
        provider: "cloudinary",
        provider_metadata: { bucket: "images" },
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        documentId: "logo-1",
        publishedAt: "2024-01-01T00:00:00.000Z",
      };

      expect(mockLogo.id).toBe(1);
      expect(mockLogo.name).toBe("company-logo.png");
      expect(mockLogo.alternativeText).toBe("Company Logo");
      expect(mockLogo.width).toBe(200);
      expect(mockLogo.height).toBe(100);
      expect(mockLogo.formats.thumbnail?.url).toBe("https://example.com/thumbnail.png");
      expect(mockLogo.formats.small?.url).toBe("https://example.com/small.png");
      expect(mockLogo.url).toBe("https://example.com/company-logo.png");
    });

    it("should allow optional format properties", () => {
      const mockLogo: TechnologyLogo = {
        id: 2,
        name: "simple-logo.jpg",
        alternativeText: "Simple Logo",
        caption: "",
        width: 150,
        height: 75,
        formats: {
          medium: {
            ext: ".jpg",
            url: "https://example.com/medium.jpg",
            hash: "medium456",
            mime: "image/jpeg",
            name: "medium_simple-logo.jpg",
            path: "/uploads",
            size: 15360,
            width: 150,
            height: 75,
          },
          large: {
            ext: ".jpg",
            url: "https://example.com/large.jpg",
            hash: "large789",
            mime: "image/jpeg",
            name: "large_simple-logo.jpg",
            path: "/uploads",
            size: 30720,
            width: 300,
            height: 150,
          },
          squared: {
            ext: ".jpg",
            url: "https://example.com/squared.jpg",
            hash: "squared123",
            mime: "image/jpeg",
            name: "squared_simple-logo.jpg",
            path: "/uploads",
            size: 20480,
            width: 150,
            height: 150,
          },
        },
        hash: "def456ghi789",
        ext: ".jpg",
        mime: "image/jpeg",
        size: 40960,
        url: "https://example.com/simple-logo.jpg",
        previewUrl: null,
        provider: "local",
        provider_metadata: null,
        createdAt: "2024-01-02T00:00:00.000Z",
        updatedAt: "2024-01-02T00:00:00.000Z",
        documentId: "logo-2",
        publishedAt: "2024-01-02T00:00:00.000Z",
      };

      expect(mockLogo.formats.medium?.url).toBe("https://example.com/medium.jpg");
      expect(mockLogo.formats.large?.url).toBe("https://example.com/large.jpg");
      expect(mockLogo.formats.squared?.url).toBe("https://example.com/squared.jpg");
      expect(mockLogo.formats.thumbnail).toBeUndefined();
      expect(mockLogo.formats.small).toBeUndefined();
      expect(mockLogo.previewUrl).toBeNull();
      expect(mockLogo.provider_metadata).toBeNull();
    });

    it("should allow empty formats object", () => {
      const mockLogo: TechnologyLogo = {
        id: 3,
        name: "basic-logo.svg",
        alternativeText: "Basic Logo",
        caption: "Basic SVG logo",
        width: 100,
        height: 100,
        formats: {},
        hash: "ghi789jkl012",
        ext: ".svg",
        mime: "image/svg+xml",
        size: 2048,
        url: "https://example.com/basic-logo.svg",
        previewUrl: null,
        provider: "local",
        provider_metadata: null,
        createdAt: "2024-01-03T00:00:00.000Z",
        updatedAt: "2024-01-03T00:00:00.000Z",
        documentId: "logo-3",
        publishedAt: "2024-01-03T00:00:00.000Z",
      };

      expect(mockLogo.formats).toEqual({});
      expect(mockLogo.ext).toBe(".svg");
      expect(mockLogo.mime).toBe("image/svg+xml");
    });
  });

  describe("Technology", () => {
    it("should have correct structure", () => {
      const mockTechnology: Technology = {
        id: 1,
        title: "React",
        link: "https://reactjs.org",
        description: "A JavaScript library for building user interfaces",
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        publishedAt: "2024-01-01T00:00:00.000Z",
        documentId: "tech-1",
        logo: {
          id: 1,
          name: "react-logo.png",
          alternativeText: "React Logo",
          caption: "React library logo",
          width: 200,
          height: 200,
          formats: {
            small: {
              ext: ".png",
              url: "https://example.com/react-small.png",
              hash: "react123",
              mime: "image/png",
              name: "small_react-logo.png",
              path: null,
              size: 8192,
              width: 100,
              height: 100,
            },
          },
          hash: "react456def789",
          ext: ".png",
          mime: "image/png",
          size: 16384,
          url: "https://example.com/react-logo.png",
          previewUrl: null,
          provider: "local",
          provider_metadata: null,
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          documentId: "logo-react",
          publishedAt: "2024-01-01T00:00:00.000Z",
        },
      };

      expect(mockTechnology.id).toBe(1);
      expect(mockTechnology.title).toBe("React");
      expect(mockTechnology.link).toBe("https://reactjs.org");
      expect(mockTechnology.description).toBe("A JavaScript library for building user interfaces");
      expect(mockTechnology.logo.name).toBe("react-logo.png");
      expect(mockTechnology.logo.alternativeText).toBe("React Logo");
    });

    it("should allow empty description", () => {
      const mockTechnology: Technology = {
        id: 2,
        title: "Vue.js",
        link: "vuejs.org",
        description: "",
        createdAt: "2024-01-02T00:00:00.000Z",
        updatedAt: "2024-01-02T00:00:00.000Z",
        publishedAt: "2024-01-02T00:00:00.000Z",
        documentId: "tech-2",
        logo: {
          id: 2,
          name: "vue-logo.svg",
          alternativeText: "Vue.js Logo",
          caption: "",
          width: 150,
          height: 150,
          formats: {},
          hash: "vue789ghi012",
          ext: ".svg",
          mime: "image/svg+xml",
          size: 4096,
          url: "https://example.com/vue-logo.svg",
          previewUrl: null,
          provider: "local",
          provider_metadata: null,
          createdAt: "2024-01-02T00:00:00.000Z",
          updatedAt: "2024-01-02T00:00:00.000Z",
          documentId: "logo-vue",
          publishedAt: "2024-01-02T00:00:00.000Z",
        },
      };

      expect(mockTechnology.description).toBe("");
      expect(mockTechnology.link).toBe("vuejs.org");
      expect(mockTechnology.logo.formats).toEqual({});
    });
  });

  describe("TechnologiesApiResponse", () => {
    it("should have correct structure", () => {
      const mockResponse: TechnologiesApiResponse = {
        data: [
          {
            id: 1,
            title: "Angular",
            link: "https://angular.io",
            description: "Platform for building mobile and desktop web applications",
            createdAt: "2024-01-01T00:00:00.000Z",
            updatedAt: "2024-01-01T00:00:00.000Z",
            publishedAt: "2024-01-01T00:00:00.000Z",
            documentId: "tech-angular",
            logo: {
              id: 1,
              name: "angular-logo.png",
              alternativeText: "Angular Logo",
              caption: "Angular framework logo",
              width: 180,
              height: 180,
              formats: {
                thumbnail: {
                  ext: ".png",
                  url: "https://example.com/angular-thumb.png",
                  hash: "angular123",
                  mime: "image/png",
                  name: "thumb_angular-logo.png",
                  path: null,
                  size: 6144,
                  width: 60,
                  height: 60,
                },
              },
              hash: "angular456def789",
              ext: ".png",
              mime: "image/png",
              size: 12288,
              url: "https://example.com/angular-logo.png",
              previewUrl: null,
              provider: "local",
              provider_metadata: null,
              createdAt: "2024-01-01T00:00:00.000Z",
              updatedAt: "2024-01-01T00:00:00.000Z",
              documentId: "logo-angular",
              publishedAt: "2024-01-01T00:00:00.000Z",
            },
          },
        ],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 1,
          },
        },
      };

      expect(mockResponse.data).toHaveLength(1);
      expect(mockResponse.data[0].title).toBe("Angular");
      expect(mockResponse.meta.pagination.total).toBe(1);
      expect(mockResponse.meta.pagination.page).toBe(1);
      expect(mockResponse.meta.pagination.pageSize).toBe(25);
      expect(mockResponse.meta.pagination.pageCount).toBe(1);
    });

    it("should allow empty data array", () => {
      const mockResponse: TechnologiesApiResponse = {
        data: [],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 0,
            total: 0,
          },
        },
      };

      expect(mockResponse.data).toHaveLength(0);
      expect(mockResponse.meta.pagination.total).toBe(0);
      expect(mockResponse.meta.pagination.pageCount).toBe(0);
    });

    it("should handle multiple technologies", () => {
      const mockResponse: TechnologiesApiResponse = {
        data: [
          {
            id: 1,
            title: "React",
            link: "https://reactjs.org",
            description: "JavaScript library",
            createdAt: "2024-01-01T00:00:00.000Z",
            updatedAt: "2024-01-01T00:00:00.000Z",
            publishedAt: "2024-01-01T00:00:00.000Z",
            documentId: "tech-1",
            logo: {} as TechnologyLogo,
          },
          {
            id: 2,
            title: "Vue.js",
            link: "https://vuejs.org",
            description: "Progressive framework",
            createdAt: "2024-01-02T00:00:00.000Z",
            updatedAt: "2024-01-02T00:00:00.000Z",
            publishedAt: "2024-01-02T00:00:00.000Z",
            documentId: "tech-2",
            logo: {} as TechnologyLogo,
          },
        ],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 2,
          },
        },
      };

      expect(mockResponse.data).toHaveLength(2);
      expect(mockResponse.data[0].title).toBe("React");
      expect(mockResponse.data[1].title).toBe("Vue.js");
      expect(mockResponse.meta.pagination.total).toBe(2);
    });
  });
});
