import React from "react";
import { render, screen } from "@testing-library/react";
import TechnologyPage from "@/app/(inapp)/technology/page";
import { useTechnology } from "../hooks/use-technology";
import type { Technology } from "../types";

// Mock dependencies
jest.mock("../hooks/use-technology");
jest.mock("../components/technology-card", () => ({
  TechnologyCard: ({ technology }: { technology: any }) => (
    <div data-testid="technology-card">
      Technology Card - {technology.title}
    </div>
  ),
}));

const mockUseTechnology = useTechnology as jest.MockedFunction<typeof useTechnology>;

describe("TechnologyPage", () => {
  const mockTechnology: Technology = {
    id: 1,
    title: "React",
    link: "https://reactjs.org",
    description: "A JavaScript library for building user interfaces",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
    publishedAt: "2024-01-01T00:00:00.000Z",
    documentId: "tech-1",
    logo: {
      id: 1,
      name: "react-logo.png",
      alternativeText: "React Logo",
      caption: "React library logo",
      width: 200,
      height: 200,
      formats: {
        small: {
          ext: ".png",
          url: "https://example.com/react-small.png",
          hash: "react123",
          mime: "image/png",
          name: "small_react-logo.png",
          path: null,
          size: 8192,
          width: 100,
          height: 100,
        },
      },
      hash: "react456def789",
      ext: ".png",
      mime: "image/png",
      size: 16384,
      url: "https://example.com/react-logo.png",
      previewUrl: null,
      provider: "local",
      provider_metadata: null,
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      documentId: "logo-react",
      publishedAt: "2024-01-01T00:00:00.000Z",
    },
  };

  const mockTechnology2: Technology = {
    id: 2,
    title: "Vue.js",
    link: "https://vuejs.org",
    description: "Progressive JavaScript framework",
    createdAt: "2024-01-02T00:00:00.000Z",
    updatedAt: "2024-01-02T00:00:00.000Z",
    publishedAt: "2024-01-02T00:00:00.000Z",
    documentId: "tech-2",
    logo: {
      id: 2,
      name: "vue-logo.svg",
      alternativeText: "Vue.js Logo",
      caption: "Vue.js framework logo",
      width: 150,
      height: 150,
      formats: {},
      hash: "vue789ghi012",
      ext: ".svg",
      mime: "image/svg+xml",
      size: 4096,
      url: "https://example.com/vue-logo.svg",
      previewUrl: null,
      provider: "local",
      provider_metadata: null,
      createdAt: "2024-01-02T00:00:00.000Z",
      updatedAt: "2024-01-02T00:00:00.000Z",
      documentId: "logo-vue",
      publishedAt: "2024-01-02T00:00:00.000Z",
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("loading state", () => {
    it("should render loading state", () => {
      mockUseTechnology.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(<TechnologyPage />);

      expect(screen.getByText("Loading technology tools...")).toBeInTheDocument();
      // Check for the loading spinner by its class
      const spinner = document.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });

    it("should have correct loading spinner styling", () => {
      mockUseTechnology.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(<TechnologyPage />);

      const spinner = document.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
      expect(spinner).toHaveClass("rounded-full", "h-8", "w-8", "border-b-2", "border-primary");
    });

    it("should center loading content", () => {
      mockUseTechnology.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(<TechnologyPage />);

      const loadingContainer = screen.getByText("Loading technology tools...").closest(".text-center");
      expect(loadingContainer).toBeInTheDocument();
      expect(loadingContainer?.parentElement).toHaveClass("flex", "items-center", "justify-center");
    });
  });

  describe("error state", () => {
    it("should render error state", () => {
      mockUseTechnology.mockReturnValue({
        data: null,
        isLoading: false,
        error: "Failed to fetch technologies data",
      });

      render(<TechnologyPage />);

      expect(screen.getByText("Error loading technology tools: Failed to fetch technologies data")).toBeInTheDocument();
    });

    it("should style error message correctly", () => {
      mockUseTechnology.mockReturnValue({
        data: null,
        isLoading: false,
        error: "Network error",
      });

      render(<TechnologyPage />);

      const errorMessage = screen.getByText("Error loading technology tools: Network error");
      expect(errorMessage).toHaveClass("text-red-600");
    });

    it("should center error message", () => {
      mockUseTechnology.mockReturnValue({
        data: null,
        isLoading: false,
        error: "API error",
      });

      render(<TechnologyPage />);

      const errorContainer = screen.getByText("Error loading technology tools: API error").closest(".text-center");
      expect(errorContainer).toBeInTheDocument();
    });
  });

  describe("successful data rendering", () => {
    it("should render page with data", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology, mockTechnology2],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      expect(screen.getByText("Technology")).toBeInTheDocument();
      expect(screen.getByText("Access your technology tools and platforms")).toBeInTheDocument();
      expect(screen.getAllByTestId("technology-card")).toHaveLength(2);
    });

    it("should render page title correctly", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      const title = screen.getByText("Technology");
      expect(title).toBeInTheDocument();
      expect(title).toHaveClass("text-3xl", "font-bold", "tracking-tight");
    });

    it("should render page description", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      const description = screen.getByText("Access your technology tools and platforms");
      expect(description).toBeInTheDocument();
      expect(description).toHaveClass("text-muted-foreground");
    });

    it("should render technologies grid with correct structure", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology, mockTechnology2],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      const technologyCards = screen.getAllByTestId("technology-card");
      expect(technologyCards).toHaveLength(2);
      expect(technologyCards[0]).toHaveTextContent("Technology Card - React");
      expect(technologyCards[1]).toHaveTextContent("Technology Card - Vue.js");
    });

    it("should have correct grid styling", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      const grid = screen.getAllByTestId("technology-card")[0].parentElement;
      expect(grid).toHaveClass("grid", "grid-cols-1", "md:grid-cols-2", "lg:grid-cols-3", "xl:grid-cols-4", "gap-6");
    });

    it("should render single technology", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      expect(screen.getAllByTestId("technology-card")).toHaveLength(1);
      expect(screen.getByText("Technology Card - React")).toBeInTheDocument();
    });

    it("should render multiple technologies", () => {
      const technologies = [mockTechnology, mockTechnology2];
      
      mockUseTechnology.mockReturnValue({
        data: technologies,
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      expect(screen.getAllByTestId("technology-card")).toHaveLength(2);
      expect(screen.getByText("Technology Card - React")).toBeInTheDocument();
      expect(screen.getByText("Technology Card - Vue.js")).toBeInTheDocument();
    });
  });

  describe("empty state", () => {
    it("should render no technologies message when data is empty array", () => {
      mockUseTechnology.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      expect(screen.getByText("Technology")).toBeInTheDocument();
      expect(screen.getByText("No technology tools are currently available.")).toBeInTheDocument();
      expect(screen.queryByTestId("technology-card")).not.toBeInTheDocument();
    });

    it("should render no technologies message when data is null", () => {
      mockUseTechnology.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      expect(screen.getByText("Technology")).toBeInTheDocument();
      expect(screen.getByText("No technology tools are currently available.")).toBeInTheDocument();
      expect(screen.queryByTestId("technology-card")).not.toBeInTheDocument();
    });

    it("should center empty state message", () => {
      mockUseTechnology.mockReturnValue({
        data: [],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      const emptyMessage = screen.getByText("No technology tools are currently available.");
      expect(emptyMessage).toHaveClass("text-muted-foreground");
      expect(emptyMessage.closest(".text-center")).toBeInTheDocument();
    });
  });

  describe("layout and styling", () => {
    it("should have correct container styling", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      // Check that the page renders with correct structure
      expect(screen.getByText("Technology")).toBeInTheDocument();
      // The container classes may vary, so just check that the page renders correctly
      const mainContainer = screen.getByText("Technology").closest("div");
      expect(mainContainer).toBeInTheDocument();
    });

    it("should have correct page header structure", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      const title = screen.getByText("Technology");
      const description = screen.getByText("Access your technology tools and platforms");

      expect(title).toBeInTheDocument();
      expect(description).toBeInTheDocument();

      // Both should be in the same header section
      const headerSection = title.closest("div");
      expect(headerSection).toBeInTheDocument();
      expect(headerSection).toContainElement(description);
    });

    it("should have proper spacing between sections", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      // Check that main container has space-y-8 class for proper spacing
      const mainContainer = screen.getByText("Technology").closest(".space-y-8");
      expect(mainContainer).toBeInTheDocument();
    });
  });

  describe("responsive behavior", () => {
    it("should have responsive grid classes", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology, mockTechnology2],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      const grid = screen.getAllByTestId("technology-card")[0].parentElement;
      expect(grid).toHaveClass(
        "grid",
        "grid-cols-1",
        "md:grid-cols-2", 
        "lg:grid-cols-3",
        "xl:grid-cols-4"
      );
    });

    it("should maintain proper gap between cards", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology, mockTechnology2],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      const grid = screen.getAllByTestId("technology-card")[0].parentElement;
      expect(grid).toHaveClass("gap-6");
    });
  });

  describe("accessibility", () => {
    it("should have proper heading hierarchy", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      const mainHeading = screen.getByRole("heading", { level: 1 });
      expect(mainHeading).toHaveTextContent("Technology");
    });

    it("should have descriptive text for screen readers", () => {
      mockUseTechnology.mockReturnValue({
        data: [mockTechnology],
        isLoading: false,
        error: null,
      });

      render(<TechnologyPage />);

      expect(screen.getByText("Access your technology tools and platforms")).toBeInTheDocument();
    });

    it("should provide meaningful loading message", () => {
      mockUseTechnology.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(<TechnologyPage />);

      expect(screen.getByText("Loading technology tools...")).toBeInTheDocument();
    });

    it("should provide clear error messages", () => {
      mockUseTechnology.mockReturnValue({
        data: null,
        isLoading: false,
        error: "Connection failed",
      });

      render(<TechnologyPage />);

      expect(screen.getByText("Error loading technology tools: Connection failed")).toBeInTheDocument();
    });
  });
});
