import { renderHook, waitFor } from "@testing-library/react";
import { useTechnology } from "../hooks/use-technology";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { getAuthHeaders } from "@/shared/lib/auth";
import type { TechnologiesApiResponse, Technology } from "../types";

// Mock dependencies
jest.mock("@/shared/contexts/auth-context");
jest.mock("@/shared/lib/auth");

const mockUseAuthContext = useAuthContext as jest.MockedFunction<typeof useAuthContext>;
const mockGetAuthHeaders = getAuthHeaders as jest.MockedFunction<typeof getAuthHeaders>;

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe("useTechnology", () => {
  const mockTechnology: Technology = {
    id: 1,
    title: "React",
    link: "https://reactjs.org",
    description: "A JavaScript library for building user interfaces",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
    publishedAt: "2024-01-01T00:00:00.000Z",
    documentId: "tech-1",
    logo: {
      id: 1,
      name: "react-logo.png",
      alternativeText: "React Logo",
      caption: "React library logo",
      width: 200,
      height: 200,
      formats: {
        small: {
          ext: ".png",
          url: "https://example.com/react-small.png",
          hash: "react123",
          mime: "image/png",
          name: "small_react-logo.png",
          path: null,
          size: 8192,
          width: 100,
          height: 100,
        },
      },
      hash: "react456def789",
      ext: ".png",
      mime: "image/png",
      size: 16384,
      url: "https://example.com/react-logo.png",
      previewUrl: null,
      provider: "local",
      provider_metadata: null,
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      documentId: "logo-react",
      publishedAt: "2024-01-01T00:00:00.000Z",
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockGetAuthHeaders.mockReturnValue({
      "Authorization": "Bearer mock-token",
      "Content-Type": "application/json",
    });

    // Mock environment variable
    process.env.NEXT_PUBLIC_API_URL = "http://localhost:1339";
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("initialization", () => {
    it("should initialize with correct default state", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: false,
          initialized: false,
        },
      } as any);

      const { result } = renderHook(() => useTechnology());

      expect(result.current.data).toBeNull();
      expect(result.current.isLoading).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it("should not fetch data when user is not authenticated", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: false,
          initialized: true,
        },
      } as any);

      renderHook(() => useTechnology());

      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should not fetch data when auth is not initialized", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: false,
        },
      } as any);

      renderHook(() => useTechnology());

      expect(mockFetch).not.toHaveBeenCalled();
    });
  });

  describe("successful data fetching", () => {
    it("should fetch and set data successfully", async () => {
      const mockResponse: TechnologiesApiResponse = {
        data: [mockTechnology],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 1,
          },
        },
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const { result } = renderHook(() => useTechnology());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toEqual([mockTechnology]);
      expect(result.current.error).toBeNull();
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:1339/technologies?populate=*",
        {
          headers: {
            "Authorization": "Bearer mock-token",
            "Content-Type": "application/json",
          },
          cache: 'no-store',
        }
      );
    });

    it("should handle empty technologies array", async () => {
      const mockResponse: TechnologiesApiResponse = {
        data: [],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 0,
            total: 0,
          },
        },
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const { result } = renderHook(() => useTechnology());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toEqual([]);
      expect(result.current.error).toBeNull();
    });

    it("should handle multiple technologies", async () => {
      const mockTechnology2: Technology = {
        ...mockTechnology,
        id: 2,
        title: "Vue.js",
        link: "https://vuejs.org",
        description: "Progressive JavaScript framework",
        documentId: "tech-2",
      };

      const mockResponse: TechnologiesApiResponse = {
        data: [mockTechnology, mockTechnology2],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 2,
          },
        },
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const { result } = renderHook(() => useTechnology());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toHaveLength(2);
      expect(result.current.data?.[0].title).toBe("React");
      expect(result.current.data?.[1].title).toBe("Vue.js");
      expect(result.current.error).toBeNull();
    });
  });

  describe("error handling", () => {
    it("should handle HTTP error responses", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: "Not Found",
      } as Response);

      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

      const { result } = renderHook(() => useTechnology());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toBeNull();
      expect(result.current.error).toBe("Failed to fetch technologies data");
      expect(consoleSpy).toHaveBeenCalledWith("Error fetching technologies:", expect.any(Error));

      consoleSpy.mockRestore();
    });

    it("should handle network errors", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      const networkError = new Error("Network error");
      mockFetch.mockRejectedValueOnce(networkError);

      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

      const { result } = renderHook(() => useTechnology());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toBeNull();
      expect(result.current.error).toBe("Network error");
      expect(consoleSpy).toHaveBeenCalledWith("Error fetching technologies:", networkError);

      consoleSpy.mockRestore();
    });

    it("should handle non-Error exceptions", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockRejectedValueOnce("String error");

      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

      const { result } = renderHook(() => useTechnology());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toBeNull();
      expect(result.current.error).toBe("An error occurred");
      expect(consoleSpy).toHaveBeenCalledWith("Error fetching technologies:", "String error");

      consoleSpy.mockRestore();
    });
  });

  describe("auth state changes", () => {
    it("should refetch data when auth state changes from unauthenticated to authenticated", async () => {
      const mockResponse: TechnologiesApiResponse = {
        data: [mockTechnology],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 1,
          },
        },
      };

      // Start with unauthenticated state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: false,
          initialized: true,
        },
      } as any);

      const { result, rerender } = renderHook(() => useTechnology());

      // Initially should not fetch when unauthenticated
      expect(mockFetch).not.toHaveBeenCalled();

      // Change to authenticated state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      rerender();

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result.current.data).toEqual([mockTechnology]);
    });

    it("should refetch data when initialization state changes", async () => {
      const mockResponse: TechnologiesApiResponse = {
        data: [mockTechnology],
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 1,
          },
        },
      };

      // Start with uninitialized state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: false,
        },
      } as any);

      const { result, rerender } = renderHook(() => useTechnology());

      // Initially should not fetch when uninitialized
      expect(mockFetch).not.toHaveBeenCalled();

      // Change to initialized state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      rerender();

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result.current.data).toEqual([mockTechnology]);
    });
  });

  describe("loading states", () => {
    it("should set loading to true when starting fetch", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      // Create a promise that we can control
      let resolvePromise: (value: any) => void;
      const fetchPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      mockFetch.mockReturnValueOnce(fetchPromise as any);

      const { result } = renderHook(() => useTechnology());

      // Initially loading should be true
      expect(result.current.isLoading).toBe(true);

      // Resolve the promise
      resolvePromise!({
        ok: true,
        json: async () => ({ data: [mockTechnology], meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 } } }),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });
    });

    it("should reset error state when starting new fetch", async () => {
      // Start with authenticated state that will fail
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      // First request fails
      mockFetch.mockRejectedValueOnce(new Error("First error"));

      const { result, unmount } = renderHook(() => useTechnology());

      await waitFor(() => {
        expect(result.current.error).toBe("First error");
      });

      // Unmount and remount with successful response
      unmount();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: [mockTechnology], meta: { pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 } } }),
      } as Response);

      const { result: newResult } = renderHook(() => useTechnology());

      await waitFor(() => {
        expect(newResult.current.error).toBeNull();
        expect(newResult.current.data).toEqual([mockTechnology]);
      });
    });
  });
});
