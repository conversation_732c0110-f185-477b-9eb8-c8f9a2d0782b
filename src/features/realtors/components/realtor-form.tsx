"use client";

import React, { useState, useRef, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Checkbox } from "@/shared/ui/checkbox";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { Loader2, Save, Upload, User } from "lucide-react";
import { realtorFormSchema, type RealtorFormData } from "../lib/validation";
import { validatePhotoFile, createFilePreview, revokeFilePreview } from "../lib/utils";
import { useRealtors } from "../hooks/use-realtors";
import type { Realtor } from "../types";

interface RealtorFormProps {
  initialRealtors?: Realtor[];
  onRealtorAdded?: (realtor: Realtor) => void;
  editMode?: boolean;
  realtorToEdit?: Realtor | null;
  onRealtorUpdated?: (realtor: Realtor) => void;
  onCancel?: () => void;
}

export function RealtorForm({ 
  initialRealtors = [], 
  onRealtorAdded, 
  editMode = false, 
  realtorToEdit = null,
  onRealtorUpdated,
  onCancel 
}: RealtorFormProps) {
  const [selectedPhoto, setSelectedPhoto] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [photoError, setPhotoError] = useState<string | null>(null);
  const [useDefaultPhoto, setUseDefaultPhoto] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { processing, addRealtor, updateRealtor } = useRealtors(initialRealtors);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<RealtorFormData>({
    resolver: zodResolver(realtorFormSchema),
    defaultValues: {
      firstname: "",
      middlename: "",
      lastname: "",
      position: "",
      email: "",
      phone: "",
      company: "",
      website: "",
      useDefaultPhoto: false,
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (editMode && realtorToEdit) {
      setValue("firstname", realtorToEdit.firstname || "");
      setValue("middlename", realtorToEdit.middlename || "");
      setValue("lastname", realtorToEdit.lastname || "");
      setValue("position", realtorToEdit.position || "");
      setValue("email", realtorToEdit.email || "");
      setValue("phone", realtorToEdit.phone || "");
      setValue("company", realtorToEdit.company || "");
      setValue("website", realtorToEdit.website || "");
      
      // Set photo preview if exists
      if (realtorToEdit.photo?.url) {
        setPhotoPreview(realtorToEdit.photo.url);
      }
    }
  }, [editMode, realtorToEdit, setValue]);

  const handlePhotoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = validatePhotoFile(file);
    if (!validation.isValid) {
      setPhotoError(validation.error || "Invalid file");
      return;
    }

    setPhotoError(null);
    setSelectedPhoto(file);
    
    // Clean up previous preview
    if (photoPreview) {
      revokeFilePreview(photoPreview);
    }
    
    const preview = createFilePreview(file);
    setPhotoPreview(preview);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (!file) return;

    const validation = validatePhotoFile(file);
    if (!validation.isValid) {
      setPhotoError(validation.error || "Invalid file");
      return;
    }

    setPhotoError(null);
    setSelectedPhoto(file);
    
    // Clean up previous preview
    if (photoPreview) {
      revokeFilePreview(photoPreview);
    }
    
    const preview = createFilePreview(file);
    setPhotoPreview(preview);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleFormSubmit = async (data: RealtorFormData) => {
    // Validate that either a photo is selected, default photo is checked, or existing photo exists (for edit mode)
    if (!selectedPhoto && !useDefaultPhoto && !realtorToEdit?.photo?.id) {
      setPhotoError("Please either upload a photo or select 'use default photo'");
      return;
    }

    try {
      if (editMode && realtorToEdit?.documentId) {
        const updatedRealtor = await updateRealtor(realtorToEdit.documentId, data, selectedPhoto, useDefaultPhoto, realtorToEdit);
        if (onRealtorUpdated) {
          onRealtorUpdated(updatedRealtor);
        }
      } else {
        const savedRealtor = await addRealtor(data, selectedPhoto, useDefaultPhoto);
        if (onRealtorAdded) {
          onRealtorAdded(savedRealtor);
        }
      }

      // Reset form only if not in edit mode
      if (!editMode) {
        reset();
        setSelectedPhoto(null);
        if (photoPreview) {
          revokeFilePreview(photoPreview);
          setPhotoPreview(null);
        }
        setUseDefaultPhoto(false);
        setPhotoError(null);
        
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    } catch (error) {
      console.error("Error saving realtor:", error);
    }
  };

  const showPhotoUpload = !useDefaultPhoto;

  const formContent = (
    <>
      {processing.isVisible && (
        <Alert className="mb-6">
          <Loader2 className="h-4 w-4 animate-spin" />
          <AlertDescription>{processing.message}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstname">First Name</Label>
              <Input
                id="firstname"
                {...register("firstname")}
                className={errors.firstname ? "border-red-500" : ""}
              />
              {errors.firstname && (
                <p className="text-sm text-red-500">{errors.firstname.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="middlename">Middle Name</Label>
              <Input id="middlename" {...register("middlename")} />
              {errors.middlename && (
                <p className="text-sm text-red-500">{errors.middlename.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastname">Last Name</Label>
              <Input
                id="lastname"
                {...register("lastname")}
                className={errors.lastname ? "border-red-500" : ""}
              />
              {errors.lastname && (
                <p className="text-sm text-red-500">{errors.lastname.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="position">Position</Label>
              <Input
                id="position"
                placeholder="I.E: Mortgage Broker"
                {...register("position")}
              />
              {errors.position && (
                <p className="text-sm text-red-500">{errors.position.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                {...register("email")}
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="************"
                {...register("phone")}
              />
              {errors.phone && (
                <p className="text-sm text-red-500">{errors.phone.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="company">Company</Label>
              <Input
                id="company"
                placeholder="Company Name"
                {...register("company")}
              />
              {errors.company && (
                <p className="text-sm text-red-500">{errors.company.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                placeholder="johnmortgage.ca"
                {...register("website")}
              />
              {errors.website && (
                <p className="text-sm text-red-500">{errors.website.message}</p>
              )}
            </div>
          </div>

          {/* Photo Upload Section */}
          <div className="space-y-4">
            <Label>Realtor Photo</Label>
            <p className="text-sm text-muted-foreground">
              <strong>JPG or PNG files only.</strong>
            </p>

            {/* Photo Preview */}
            {(photoPreview || useDefaultPhoto) && (
              <div className="flex justify-center">
                <img
                  src={photoPreview || "/images/indi-symbol.svg"}
                  alt="Realtor preview"
                  className="w-32 h-32 object-cover rounded-lg border"
                />
              </div>
            )}

            {/* Photo Upload Area */}
            {showPhotoUpload && (
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p className="text-sm text-gray-600">
                  {selectedPhoto
                    ? selectedPhoto.name
                    : "Drag/drop your image file here or click to choose it."}
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/jpeg,image/jpg,image/png"
                  onChange={handlePhotoSelect}
                  className="hidden"
                />
              </div>
            )}

            {/* Default Photo Option */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="useDefaultPhoto"
                checked={useDefaultPhoto}
                onCheckedChange={(checked) => {
                  setUseDefaultPhoto(checked as boolean);
                  if (checked) {
                    setSelectedPhoto(null);
                    if (photoPreview) {
                      revokeFilePreview(photoPreview);
                      setPhotoPreview(null);
                    }
                    setPhotoError(null); // Clear photo error when default photo is selected
                  }
                }}
              />
              <Label htmlFor="useDefaultPhoto" className="text-sm">
                I don't have a photo right now. Please use the default image.
              </Label>
            </div>

            {/* Photo Error */}
            {photoError && (
              <Alert variant="destructive">
                <AlertDescription>{photoError}</AlertDescription>
              </Alert>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-2">
            {onCancel && !editMode && (
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={processing.isVisible || (photoError !== null)}
              className="min-w-32"
            >
              {processing.isVisible ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {editMode ? 'Updating...' : 'Saving...'}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {editMode ? 'Update' : 'Save'}
                </>
              )}
            </Button>
          </div>
        </form>
      </>
    );

  // Render with or without Card wrapper based on edit mode
  if (editMode) {
    return formContent;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Add a Realtor
        </CardTitle>
      </CardHeader>
      <CardContent>
        {formContent}
      </CardContent>
    </Card>
  );
}
