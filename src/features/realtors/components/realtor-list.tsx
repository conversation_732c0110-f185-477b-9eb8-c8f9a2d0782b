"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardFooter } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Trash2, Mail, Phone, Globe, Building2, User, Edit, X } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/shared/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import { useRealtors } from "../hooks/use-realtors";
import { RealtorForm } from "./realtor-form";
import type { Realtor } from "../types";

interface RealtorListProps {
  realtors: Realtor[];
  onRealtorDeleted?: (realtorId: string) => void;
  onRealtorUpdated?: (realtor: Realtor) => void;
}

export function RealtorList({ realtors: initialRealtors, onRealtorDeleted, onRealtorUpdated }: RealtorListProps) {
  const { realtors, processing, deleteRealtor, fetchRealtor } = useRealtors(initialRealtors);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [editingRealtor, setEditingRealtor] = useState<Realtor | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isLoadingRealtor, setIsLoadingRealtor] = useState(false);

  // Sort realtors alphabetically by first name
  const sortedRealtors = [...realtors].sort((a, b) => 
    (a.firstname || "").localeCompare(b.firstname || "")
  );

  const handleEdit = async (realtor: Realtor) => {
    if (!realtor.documentId) {
      console.error('No documentId found for realtor');
      return;
    }

    setIsLoadingRealtor(true);
    try {
      // Fetch the full realtor data with populate=*
      const fullRealtor = await fetchRealtor(realtor.documentId);
      setEditingRealtor(fullRealtor);
      setIsEditModalOpen(true);
    } catch (error) {
      console.error('Error fetching realtor for edit:', error);
    } finally {
      setIsLoadingRealtor(false);
    }
  };

  const handleEditCancel = () => {
    setIsEditModalOpen(false);
    setEditingRealtor(null);
  };

  const handleEditSuccess = (updatedRealtor: Realtor) => {
    setIsEditModalOpen(false);
    setEditingRealtor(null);
    if (onRealtorUpdated) {
      onRealtorUpdated(updatedRealtor);
    }
  };

  const handleDelete = async (realtorId: string) => {
    setDeletingId(realtorId);
    try {
      await deleteRealtor(realtorId);
      if (onRealtorDeleted) {
        onRealtorDeleted(realtorId);
      }
    } catch (error) {
      console.error('Error deleting realtor:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const getPhotoUrl = (realtor: Realtor): string => {
    if (realtor.photo?.url) {
      return realtor.photo.url;
    }
    return "/images/indi-symbol.svg";
  };

  const formatPhoneNumber = (phone?: string): string => {
    if (!phone) return "";
    
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, "");
    
    // Format as (XXX) XXX-XXXX
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    
    return phone;
  };

  if (realtors.length === 0) {
    return (
      <div className="text-center py-12">
        <User className="h-12 w-12 mx-auto text-primary mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Realtors Added</h3>
        <p className="text-primary">
          Add your first realtor using the form above.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold">Your Realtors</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {sortedRealtors.map((realtor) => (
          <Card key={realtor.id} className="overflow-hidden hover:shadow-md transition-shadow px-0 py-4 flex flex-col justify-between">
            <CardContent>
                  {/* Photo and Name */}
                  <div className="flex space-x-4">
                    <img
                      src={getPhotoUrl(realtor)}
                      alt={`${realtor.firstname} ${realtor.lastname}`}
                      className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                    />
                    <div className="flex-1 min-w-0 space-y-2">
                      <div>
                          <h2 className="font-semibold text-lg truncate">
                            {realtor.firstname} {realtor.middlename && `${realtor.middlename} `}{realtor.lastname}
                          </h2>
                          {realtor.position && (
                            <h3 className="text-sm text-primary">
                              {realtor.position}
                            </h3>
                          )}
                      </div>

                      <div className="flex flex-col gap-1 mt-2">
                      {realtor.email && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Mail className="h-4 w-4 text-primary flex-shrink-0" />
                          <a
                            href={`mailto:${realtor.email}`}
                            className="text-slate-900 hover:underline truncate"
                          >
                            {realtor.email}
                          </a>
                        </div>
                      )}
                    

                      {realtor.phone && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Phone className="h-4 w-4 text-primary flex-shrink-0" />
                          <a
                            href={`tel:${realtor.phone}`}
                            className="text-slate-900 hover:underline"
                          >
                            {formatPhoneNumber(realtor.phone)}
                          </a>
                        </div>
                      )}

                      {realtor.company && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Building2 className="h-4 w-4 text-primary flex-shrink-0" />
                          <span className="truncate">{realtor.company}</span>
                        </div>
                      )}

                      {realtor.website && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Globe className="h-4 w-4 text-primary flex-shrink-0" />
                          <a
                            href={realtor.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-slate-900 hover:underline truncate"
                          >
                            {realtor.website.replace(/^https?:\/\//, "")}
                          </a>
                        </div>
                      )}
                      </div>
                    </div>
                  </div>

                  
                                          
            </CardContent>
            <CardFooter className="flex justify-end gap-2 pt-2">
              <Button
                variant="default"
                size="sm"
                onClick={() => handleEdit(realtor)}
                disabled={isLoadingRealtor}
                className="flex-1"
              >
                <Edit className="h-4 w-4 mr-2" /> Edit Realtor
              </Button>
              
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={deletingId === realtor.id || processing.isVisible}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Realtor</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete {realtor.firstname} {realtor.lastname}? 
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => handleDelete(realtor.id)}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Edit Realtor</span>              
            </DialogTitle>
          </DialogHeader>
          
          {editingRealtor && (
            <RealtorForm
              editMode={true}
              realtorToEdit={editingRealtor}
              onRealtorUpdated={handleEditSuccess}
              onCancel={handleEditCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
