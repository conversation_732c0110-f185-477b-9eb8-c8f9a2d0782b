import { useState, useCallback, useEffect, useRef } from "react";
import { ListingSheetApiClient } from "../lib/api-client";
import type {
  ListingSheet,
  ListingSheetCreatePayload,
  ListingSheetUpdatePayload,
  ViewMode,
  ItemsPerPage,
  ListingSheetState,
} from "../types";

interface UseListingSheetsEnhancedOptions {
  initialListingSheets?: ListingSheet[];
  initialCount?: number;
  initialPage?: number;
  initialViewMode?: ViewMode;
  initialItemsPerPage?: ItemsPerPage;
}

interface UseListingSheetsEnhancedResult extends ListingSheetState {
  setListingSheets: (sheets: ListingSheet[]) => void;
  setCurrentPage: (page: number) => void;
  setViewMode: (mode: ViewMode) => void;
  setItemsPerPage: (itemsPerPage: ItemsPerPage) => void;
  refetch: () => Promise<void>;
  createListingSheet: (
    payload: ListingSheetCreatePayload
  ) => Promise<{ data?: ListingSheet; error?: string }>;
  updateListingSheet: (
    documentId: string,
    payload: ListingSheetUpdatePayload
  ) => Promise<{ data?: ListingSheet; error?: string }>;
  deleteListingSheet: (
    documentId: string
  ) => Promise<{ success: boolean; error?: string }>;
  getListingSheet: (
    documentId: string
  ) => Promise<{ data?: ListingSheet; error?: string }>;
  getListingSheetBySlug: (
    slug: string
  ) => Promise<{ data?: ListingSheet; error?: string }>;
  getListingSheetByDocumentId: (
    documentId: string
  ) => Promise<{ data?: ListingSheet; error?: string }>;
  handlePagination: (newPage: number) => Promise<void>;
}

export function useListingSheetsEnhanced({
  initialListingSheets = [],
  initialCount = 0,
  initialPage = 1,
  initialViewMode = 'cards',
  initialItemsPerPage = 12
}: UseListingSheetsEnhancedOptions = {}): UseListingSheetsEnhancedResult {
  const [state, setState] = useState<ListingSheetState>({
    listingSheets: initialListingSheets,
    count: initialCount,
    currentPage: initialPage,
    isLoading: false,
    error: null,
    viewMode: initialViewMode,
    itemsPerPage: initialItemsPerPage
  });

  const currentPageRef = useRef(initialPage);

  // Fetch data when page or itemsPerPage changes
  useEffect(() => {
    const fetchData = async () => {
      setState(prev => ({ ...prev, isLoading: true }));

      try {
        const result = await ListingSheetApiClient.getListingSheets({
          page: currentPageRef.current,
          pageSize: state.itemsPerPage,
          sortBy: "createdAt",
          sortOrder: "desc",
        });

        if (result.error) {
          setState(prev => ({
            ...prev,
            error: result.error || 'Unknown error occurred',
            listingSheets: [],
            isLoading: false
          }));
        } else {
          setState(prev => ({
            ...prev,
            listingSheets: result.data,
            count: result.data.length, // For now, use data length as count
            error: null,
            isLoading: false
          }));
        }
      } catch (err) {
        console.error('Error in fetchData:', err);
        setState(prev => ({
          ...prev,
          error: err instanceof Error ? err.message : 'Unknown error occurred',
          listingSheets: [],
          isLoading: false
        }));
      }
    };

    fetchData();
  }, [state.itemsPerPage]);

  const setListingSheets = useCallback((listingSheets: ListingSheet[]) => {
    setState(prev => ({ ...prev, listingSheets }));
  }, []);

  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
    currentPageRef.current = page;
  }, []);

  const setViewMode = useCallback((mode: ViewMode) => {
    setState(prev => ({ ...prev, viewMode: mode }));
  }, []);

  const setItemsPerPage = useCallback((itemsPerPage: ItemsPerPage) => {
    setState(prev => ({ ...prev, itemsPerPage, currentPage: 1 }));
    currentPageRef.current = 1;
  }, []);

  const refetch = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const result = await ListingSheetApiClient.getListingSheets({
        page: currentPageRef.current,
        pageSize: state.itemsPerPage,
        sortBy: "createdAt",
        sortOrder: "desc",
      });

      if (result.error) {
        setState(prev => ({
          ...prev,
          error: result.error || 'Unknown error occurred',
          listingSheets: [],
          isLoading: false
        }));
      } else {
        setState(prev => ({
          ...prev,
          listingSheets: result.data,
            count: result.data.length, // For now, use data length as count
          error: null,
          isLoading: false
        }));
      }
    } catch (err) {
      console.error('Error in refetch:', err);
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'Unknown error occurred',
        listingSheets: [],
        isLoading: false
      }));
    }
  }, [state.itemsPerPage]);

  const createListingSheet = useCallback(
    async (payload: ListingSheetCreatePayload) => {
      try {
        setState(prev => ({ ...prev, error: null }));

        const result = await ListingSheetApiClient.createListingSheet(payload);

        if (result.data) {
          // Add the new listing sheet to the beginning of the list
          setState(prev => ({
            ...prev,
            listingSheets: [result.data!, ...prev.listingSheets],
            count: prev.count + 1
          }));
        }

        return result;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to create listing sheet";
        setState(prev => ({ ...prev, error: errorMessage }));
        return { error: errorMessage };
      }
    },
    []
  );

  const updateListingSheet = useCallback(
    async (documentId: string, payload: ListingSheetUpdatePayload) => {
      try {
        setState(prev => ({ ...prev, error: null }));

        const result = await ListingSheetApiClient.updateListingSheet(
          documentId,
          payload
        );

        if (result.data) {
          // Update the listing sheet in the list
          setState(prev => ({
            ...prev,
            listingSheets: prev.listingSheets.map((sheet) =>
              sheet.documentId === documentId ? result.data! : sheet
            )
          }));
        }

        return result;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to update listing sheet";
        setState(prev => ({ ...prev, error: errorMessage }));
        return { error: errorMessage };
      }
    },
    []
  );

  const deleteListingSheet = useCallback(async (documentId: string) => {
    try {
      setState(prev => ({ ...prev, error: null }));

      const result = await ListingSheetApiClient.deleteListingSheet(documentId);

      if (result.success) {
        // Remove the listing sheet from the list
        setState(prev => ({
          ...prev,
          listingSheets: prev.listingSheets.filter((sheet) => sheet.documentId !== documentId),
          count: Math.max(0, prev.count - 1)
        }));
      }

      return result;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to delete listing sheet";
      setState(prev => ({ ...prev, error: errorMessage }));
      return { success: false, error: errorMessage };
    }
  }, []);

  const getListingSheet = useCallback(async (documentId: string) => {
    try {
      setState(prev => ({ ...prev, error: null }));
      return await ListingSheetApiClient.getListingSheet(documentId);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch listing sheet";
      setState(prev => ({ ...prev, error: errorMessage }));
      return { error: errorMessage };
    }
  }, []);

  const getListingSheetBySlug = useCallback(async (slug: string) => {
    try {
      setState(prev => ({ ...prev, error: null }));
      return await ListingSheetApiClient.getListingSheetBySlug(slug);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch listing sheet";
      setState(prev => ({ ...prev, error: errorMessage }));
      return { error: errorMessage };
    }
  }, []);

  const getListingSheetByDocumentId = useCallback(async (documentId: string) => {
    try {
      setState(prev => ({ ...prev, error: null }));
      return await ListingSheetApiClient.getListingSheetByDocumentId(documentId);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch listing sheet";
      setState(prev => ({ ...prev, error: errorMessage }));
      return { error: errorMessage };
    }
  }, []);

  const handlePagination = useCallback(async (newPage: number) => {
    setCurrentPage(newPage);
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const result = await ListingSheetApiClient.getListingSheets({
        page: newPage,
        pageSize: state.itemsPerPage,
        sortBy: "createdAt",
        sortOrder: "desc",
      });

      if (result.error) {
        setState(prev => ({
          ...prev,
          error: result.error || 'Unknown error occurred',
          listingSheets: [],
          isLoading: false
        }));
      } else {
        setState(prev => ({
          ...prev,
          listingSheets: result.data,
            count: result.data.length, // For now, use data length as count
          error: null,
          isLoading: false
        }));
      }
    } catch (error) {
      console.error('Error in pagination:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        listingSheets: [],
        isLoading: false
      }));
    }
  }, [state.itemsPerPage, setCurrentPage]);

  return {
    ...state,
    setListingSheets,
    setCurrentPage,
    setViewMode,
    setItemsPerPage,
    refetch,
    createListingSheet,
    updateListingSheet,
    deleteListingSheet,
    getListingSheet,
    getListingSheetBySlug,
    getListingSheetByDocumentId,
    handlePagination,
  };
}
