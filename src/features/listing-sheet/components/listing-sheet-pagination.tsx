"use client";

import React, { useMemo } from "react";
import { But<PERSON> } from "@/shared/ui/button";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { cn } from "@/shared/lib/utils";

interface ListingSheetPaginationProps {
  currentPage: number;
  totalCount: number;
  itemsPerPage?: number;
  onPageChange: (page: number) => void;
  className?: string;
  disabled?: boolean;
}

export default function ListingSheetPagination({
  currentPage,
  totalCount,
  itemsPerPage = 12,
  onPageChange,
  className,
  disabled = false
}: ListingSheetPaginationProps) {
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  
  // Don't render pagination if there's only one page or no items
  if (totalPages <= 1) {
    return null;
  }

  const calculatePagination = useMemo(() => {
    if (totalPages <= 5) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const pagesToShow: (number | string)[] = [];

    if (currentPage <= 3) {
      pagesToShow.push(1, 2, 3, 4, 5);
    } else if (currentPage >= totalPages - 2) {
      pagesToShow.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
    } else {
      pagesToShow.push(currentPage - 2, currentPage - 1, currentPage, currentPage + 1, currentPage + 2);
    }

    // Add ellipsis and first/last pages if needed
    if (pagesToShow[0] as number > 2) pagesToShow.unshift('...');
    if (pagesToShow[0] !== 1) pagesToShow.unshift(1);
    if ((pagesToShow[pagesToShow.length - 1] as number) < totalPages - 1) pagesToShow.push('...');
    if (pagesToShow[pagesToShow.length - 1] !== totalPages) pagesToShow.push(totalPages);

    return pagesToShow;
  }, [currentPage, totalPages]);

  const handlePageChange = (page: number) => {
    if (disabled || page < 1 || page > totalPages || page === currentPage) {
      return;
    }
    onPageChange(page);
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  return (
    <div className={cn("flex justify-center items-center gap-2 mt-8", className)}>
      {/* Previous Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={handlePrevious}
        disabled={disabled || currentPage === 1}
        className="flex items-center gap-1"
      >
        <ChevronLeft className="h-4 w-4" />
        Previous
      </Button>

      {/* Page Numbers */}
      <div className="flex items-center gap-1">
        {calculatePagination.map((page, index) => (
          <React.Fragment key={index}>
            {page === '...' ? (
              <div className="flex items-center justify-center w-8 h-8">
                <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
              </div>
            ) : (
              <Button
                variant={currentPage === page ? "default" : "outline"}
                size="sm"
                onClick={() => handlePageChange(page as number)}
                disabled={disabled}
                className={cn(
                  "w-8 h-8 p-0",
                  currentPage === page && "bg-primary text-primary-foreground hover:bg-primary/90"
                )}
              >
                {page}
              </Button>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Next Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={handleNext}
        disabled={disabled || currentPage === totalPages}
        className="flex items-center gap-1"
      >
        Next
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
