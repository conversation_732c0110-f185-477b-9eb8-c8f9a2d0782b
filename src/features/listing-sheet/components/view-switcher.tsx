"use client";

import React from "react";
import { Button } from "@/shared/ui/button";
import { Card, CardContent } from "@/shared/ui/card";
import { LayoutGrid, Table } from "lucide-react";
import { ViewMode } from "../types";
import { cn } from "@/shared/lib/utils";

interface ViewSwitcherProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  className?: string;
  disabled?: boolean;
}

export default function ViewSwitcher({
  viewMode,
  onViewModeChange,
  className,
  disabled = false
}: ViewSwitcherProps) {
  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-muted-foreground">View:</span>
            <div className="flex items-center border rounded-md p-1">
              <Button
                variant={viewMode === 'cards' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewModeChange('cards')}
                disabled={disabled}
                className={cn(
                  "flex items-center gap-2 px-3 py-1.5 h-8",
                  viewMode === 'cards' && "bg-primary text-primary-foreground hover:bg-primary/90"
                )}
              >
                <LayoutGrid className="h-4 w-4" />
                Cards
              </Button>
              <Button
                variant={viewMode === 'table' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewModeChange('table')}
                disabled={disabled}
                className={cn(
                  "flex items-center gap-2 px-3 py-1.5 h-8",
                  viewMode === 'table' && "bg-primary text-primary-foreground hover:bg-primary/90"
                )}
              >
                <Table className="h-4 w-4" />
                Table
              </Button>
            </div>
          </div>
          
          <div className="text-sm text-muted-foreground">
            {viewMode === 'cards' ? 'Card view' : 'Table view'}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
