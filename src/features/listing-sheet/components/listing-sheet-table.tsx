"use client";

import React from "react";
import { format } from "date-fns";
import { Edit, Trash2, MapPin, DollarSign, Calendar } from "lucide-react";
import Link from "next/link";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/shared/ui/table";
import { Button } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Card, CardContent } from "@/shared/ui/card";
import { formatCurrency } from "../lib/mortgage-calculations";
import type { ListingSheet } from "../types";
import { cn } from "@/shared/lib/utils";

interface ListingSheetTableProps {
  listingSheets: ListingSheet[];
  onDelete: (sheet: ListingSheet) => void;
  className?: string;
}

export default function ListingSheetTable({ 
  listingSheets, 
  onDelete, 
  className 
}: ListingSheetTableProps) {
  if (!listingSheets || listingSheets.length === 0) {
    return (
      <div className="text-center py-8">
        <h2 className="text-xl font-semibold text-muted-foreground">No listing sheets found.</h2>
      </div>
    );
  }

  const getPropertyImage = (sheet: ListingSheet): string => {
    return (
      sheet.sheet?.pdf?.propertyPhoto?.url ||
      "/images/property-image-default.jpg"
    );
  };

  const getFormattedPrice = (sheet: ListingSheet): string => {
    return formatCurrency(sheet.sheet?.askingPrice || 0);
  };

  const getMLSCode = (sheet: ListingSheet): string => {
    return sheet.sheet?.pdf?.mlsCode || sheet.sheet?.mlsCode || "";
  };

  const getAddress = (sheet: ListingSheet): string => {
    return sheet.sheet?.pdf?.address || sheet.sheet?.address || "";
  };

  return (
    <div className={className}>
      <Card>        
        <CardContent className="px-6 py-4">                 
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[80px]">Image</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Address</TableHead>
                <TableHead>MLS Code</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="w-[120px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {listingSheets.map((sheet) => {
                const propertyImage = getPropertyImage(sheet);
                const price = getFormattedPrice(sheet);
                const mlsCode = getMLSCode(sheet);
                const address = getAddress(sheet);
                const createdAt = format(new Date(sheet.createdAt), "MMM d, yyyy");

                return (
                  <TableRow 
                    key={sheet.documentId}
                    className="cursor-pointer transition-colors hover:bg-muted/50"
                  >
                    <TableCell>
                      <div className="relative w-16 h-12 rounded-md overflow-hidden">
                        <img
                          src={propertyImage}
                          alt="Property"
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            if (!target.src.includes("property-image-default.jpg")) {
                              target.src = "/images/property-image-default.jpg";
                            } else {
                              target.src =
                                "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzlmYTJhOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==";
                            }
                          }}
                        />
                        {mlsCode && (
                          <Badge className="absolute top-1 left-1 bg-black/70 text-white text-xs px-1 py-0">
                            MLS
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center font-semibold text-lg">
                        <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                        {price}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center text-sm">
                        {address ? (
                          <>
                            <MapPin className="h-3 w-3 mr-1 text-muted-foreground" />
                            <span className="truncate max-w-[200px]">{address}</span>
                          </>
                        ) : (
                          '-'
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="text-sm">
                        {mlsCode ? (
                          <Badge variant="outline" className="text-xs">
                            {mlsCode}
                          </Badge>
                        ) : (
                          '-'
                        )}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Calendar className="h-3 w-3 mr-1" />
                        {createdAt}
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="flex gap-1">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/listing-sheet/listings/${sheet.documentId || sheet.id}`}>
                            <Edit className="h-3 w-3" />
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDelete(sheet)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
