"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer } from "@/shared/ui/card";
import { But<PERSON> } from "@/shared/ui/button";
import { Download, FileText, ExternalLink, File } from "lucide-react";
import { saveAs } from "file-saver";
import type { GroupBenefitsFile } from "../types";

interface BenefitsFileCardProps {
  file: GroupBenefitsFile;
}

export function BenefitsFileCard({ file }: BenefitsFileCardProps) {
  const handleDownload = async () => {
    if (file.file.url && file.title) {
      try {
        // Create a clean filename from the title
        const filename = `${file.title.replace(/[^a-z0-9\s]/gi, '-').toLowerCase()}${file.file.ext || ''}`;
        
        // Fetch the file and save it using file-saver
        const response = await fetch(file.file.url);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const blob = await response.blob();
        saveAs(blob, filename);
      } catch (error) {
        console.error("Download failed:", error);
        // Fallback to opening in new tab if download fails
        window.open(file.file.url, "_blank");
      }
    }
  };

  const getFileIcon = () => {
    const ext = file.file.ext.toLowerCase();
    
    return (
      <File size={64} className="text-primary/60" />
    );
  };

  const getFileSize = () => {
    const sizeInKB = file.file.size;
    if (sizeInKB < 1024) {
      return `${sizeInKB.toFixed(1)} KB`;
    }
    return `${(sizeInKB / 1024).toFixed(1)} MB`;
  };

  return (
    <Card className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] p-0 overflow-hidden flex flex-col justify-between gap-2">
      <CardContent className="p-0">
        {/* File Icon - styled like thumbnail */}
        <div className="relative aspect-video overflow-hidden rounded-t-lg bg-muted">
          <div className="w-full h-full flex items-center justify-center">
            {getFileIcon()}
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <h3 className="font-semibold text-lg leading-tight line-clamp-2 group-hover:text-primary transition-colors">
            {file.title}
          </h3>

          <div className="flex gap-2 mt-2">
            {/* File type badge */}
            {file.file.ext && (
              <div className="">
                <span className="inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-input bg-background hover:bg-accent hover:text-accent-foreground capitalize">
                  {file.file.ext.replace(".", "")}
                </span>
              </div>
            )}
          </div>

          {/* File details */}
          <div className="space-y-1 mt-2">
            <p className="text-sm text-muted-foreground">{file.file.name}</p>
            <p className="text-sm text-muted-foreground">{getFileSize()}</p>
          </div>
        </div>
      </CardContent>

      {/* Footer with Buttons */}
      <CardFooter className="p-4 pt-0">
        <div className="flex gap-2 w-full">
          <Button
            onClick={handleDownload}
            className="flex-1"
            variant="default"
          >
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button
            onClick={() => window.open(file.file.url, '_blank')}
            variant="outline"
            size="icon"
          >
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
