import React from "react";
import { render, screen } from "@testing-library/react";
import GroupBenefitsPage from "@/app/(inapp)/group-benefits/page";
import { useGroupBenefits } from "../hooks/use-group-benefits";
import type { GroupBenefitsPageData } from "../types";

// Mock dependencies
jest.mock("../hooks/use-group-benefits");
jest.mock("../components/benefits-file-card", () => ({
  BenefitsFileCard: ({ file }: { file: any }) => (
    <div data-testid="benefits-file-card">
      Benefits File Card - {file.title}
    </div>
  ),
}));
jest.mock("@/shared/components/body-text-renderer", () => ({
  BodyTextRenderer: ({ content }: { content: string }) => (
    <div data-testid="body-text-renderer">{content}</div>
  ),
}));

const mockUseGroupBenefits = useGroupBenefits as jest.MockedFunction<typeof useGroupBenefits>;

describe("GroupBenefitsPage", () => {
  const mockPageData: GroupBenefitsPageData = {
    id: 1,
    pageTitle: "Group Benefits",
    slug: "group-benefits",
    description: "Information about group benefits for employees",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
    publishedAt: "2024-01-01T00:00:00.000Z",
    documentId: "page-1",
    locale: null,
    files: [
      {
        id: 1,
        title: "Benefits Guide 2024",
        file: {
          id: 1,
          name: "benefits-guide-2024.pdf",
          alternativeText: "Benefits Guide PDF",
          caption: "Employee benefits guide for 2024",
          width: null,
          height: null,
          formats: {},
          hash: "abc123",
          ext: ".pdf",
          mime: "application/pdf",
          size: 2048,
          url: "https://example.com/benefits-guide-2024.pdf",
          previewUrl: null,
          provider: "local",
          provider_metadata: null,
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          documentId: "doc-1",
          locale: null,
          publishedAt: "2024-01-01T00:00:00.000Z",
          folderPath: null,
        },
      },
      {
        id: 2,
        title: "Health Insurance Overview",
        file: {
          id: 2,
          name: "health-insurance.pdf",
          alternativeText: "Health Insurance PDF",
          caption: "Health insurance overview",
          width: null,
          height: null,
          formats: {},
          hash: "def456",
          ext: ".pdf",
          mime: "application/pdf",
          size: 1024,
          url: "https://example.com/health-insurance.pdf",
          previewUrl: null,
          provider: "local",
          provider_metadata: null,
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          documentId: "doc-2",
          locale: null,
          publishedAt: "2024-01-01T00:00:00.000Z",
          folderPath: null,
        },
      },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("loading state", () => {
    it("should render loading state", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("Loading group benefits...")).toBeInTheDocument();
      // Check for the loading spinner by its class
      const spinner = document.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });

    it("should have correct loading spinner styling", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      render(<GroupBenefitsPage />);

      const spinner = document.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
      expect(spinner).toHaveClass("rounded-full", "h-8", "w-8", "border-b-2", "border-primary");
    });
  });

  describe("error state", () => {
    it("should render error state", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: null,
        isLoading: false,
        error: "Failed to fetch group benefits data",
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("Error loading group benefits: Failed to fetch group benefits data")).toBeInTheDocument();
    });

    it("should style error message correctly", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: null,
        isLoading: false,
        error: "Network error",
      });

      render(<GroupBenefitsPage />);

      const errorMessage = screen.getByText("Error loading group benefits: Network error");
      expect(errorMessage).toHaveClass("text-red-600");
    });
  });

  describe("no data state", () => {
    it("should render no data message when data is null", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: null,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("No group benefits data available.")).toBeInTheDocument();
    });
  });

  describe("successful data rendering", () => {
    it("should render page with data", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: mockPageData,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("Group Benefits")).toBeInTheDocument();
      expect(screen.getByTestId("body-text-renderer")).toBeInTheDocument();
      expect(screen.getByText("Available Documents")).toBeInTheDocument();
      expect(screen.getAllByTestId("benefits-file-card")).toHaveLength(2);
    });

    it("should render page title correctly", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: mockPageData,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      const title = screen.getByText("Group Benefits");
      expect(title).toBeInTheDocument();
      expect(title).toHaveClass("text-3xl", "font-bold", "tracking-tight");
    });

    it("should render description with BodyTextRenderer", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: mockPageData,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      const bodyTextRenderer = screen.getByTestId("body-text-renderer");
      expect(bodyTextRenderer).toBeInTheDocument();
      expect(bodyTextRenderer).toHaveTextContent("Information about group benefits for employees");
    });

    it("should render files grid with correct structure", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: mockPageData,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("Available Documents")).toBeInTheDocument();
      
      const fileCards = screen.getAllByTestId("benefits-file-card");
      expect(fileCards).toHaveLength(2);
      expect(fileCards[0]).toHaveTextContent("Benefits File Card - Benefits Guide 2024");
      expect(fileCards[1]).toHaveTextContent("Benefits File Card - Health Insurance Overview");
    });

    it("should have correct grid styling", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: mockPageData,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      const grid = screen.getAllByTestId("benefits-file-card")[0].parentElement;
      expect(grid).toHaveClass("grid", "grid-cols-1", "md:grid-cols-2", "lg:grid-cols-3", "xl:grid-cols-4", "gap-6");
    });
  });

  describe("edge cases", () => {
    it("should handle page data without description", () => {
      const pageDataWithoutDescription: GroupBenefitsPageData = {
        ...mockPageData,
        description: "",
      };

      mockUseGroupBenefits.mockReturnValue({
        data: pageDataWithoutDescription,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("Group Benefits")).toBeInTheDocument();
      expect(screen.queryByTestId("body-text-renderer")).not.toBeInTheDocument();
    });

    it("should handle page data with null description", () => {
      const pageDataWithNullDescription: GroupBenefitsPageData = {
        ...mockPageData,
        description: null as any,
      };

      mockUseGroupBenefits.mockReturnValue({
        data: pageDataWithNullDescription,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("Group Benefits")).toBeInTheDocument();
      expect(screen.queryByTestId("body-text-renderer")).not.toBeInTheDocument();
    });

    it("should render no files message when files array is empty", () => {
      const pageDataWithoutFiles: GroupBenefitsPageData = {
        ...mockPageData,
        files: [],
      };

      mockUseGroupBenefits.mockReturnValue({
        data: pageDataWithoutFiles,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("Group Benefits")).toBeInTheDocument();
      expect(screen.getByText("No documents are currently available.")).toBeInTheDocument();
      expect(screen.queryByText("Available Documents")).not.toBeInTheDocument();
      expect(screen.queryByTestId("benefits-file-card")).not.toBeInTheDocument();
    });

    it("should render no files message when files is null", () => {
      const pageDataWithNullFiles: GroupBenefitsPageData = {
        ...mockPageData,
        files: null as any,
      };

      mockUseGroupBenefits.mockReturnValue({
        data: pageDataWithNullFiles,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("Group Benefits")).toBeInTheDocument();
      expect(screen.getByText("No documents are currently available.")).toBeInTheDocument();
      expect(screen.queryByText("Available Documents")).not.toBeInTheDocument();
    });

    it("should handle single file", () => {
      const pageDataWithSingleFile: GroupBenefitsPageData = {
        ...mockPageData,
        files: [mockPageData.files[0]],
      };

      mockUseGroupBenefits.mockReturnValue({
        data: pageDataWithSingleFile,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      expect(screen.getByText("Available Documents")).toBeInTheDocument();
      expect(screen.getAllByTestId("benefits-file-card")).toHaveLength(1);
      expect(screen.getByText("Benefits File Card - Benefits Guide 2024")).toBeInTheDocument();
    });
  });

  describe("layout and styling", () => {
    it("should have correct container styling", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: mockPageData,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      // Check that the page renders with correct structure
      expect(screen.getByText("Group Benefits")).toBeInTheDocument();
      // The container classes may vary, so just check that the page renders correctly
      const mainContainer = screen.getByText("Group Benefits").closest("div");
      expect(mainContainer).toBeInTheDocument();
    });

    it("should have correct prose styling for description", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: mockPageData,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      const proseContainer = screen.getByTestId("body-text-renderer").closest(".prose");
      expect(proseContainer).toHaveClass("prose", "prose-gray", "max-w-none");
    });

    it("should have correct section heading styling", () => {
      mockUseGroupBenefits.mockReturnValue({
        data: mockPageData,
        isLoading: false,
        error: null,
      });

      render(<GroupBenefitsPage />);

      const sectionHeading = screen.getByText("Available Documents");
      expect(sectionHeading).toHaveClass("text-2xl", "font-semibold");
    });
  });
});
