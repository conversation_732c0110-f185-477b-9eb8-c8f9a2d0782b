import { renderHook, waitFor } from "@testing-library/react";
import { useGroupBenefits } from "../hooks/use-group-benefits";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { getAuthHeaders } from "@/shared/lib/auth";
import type { GroupBenefitsApiResponse, GroupBenefitsPageData } from "../types";

// Mock dependencies
jest.mock("@/shared/contexts/auth-context");
jest.mock("@/shared/lib/auth");

const mockUseAuthContext = useAuthContext as jest.MockedFunction<typeof useAuthContext>;
const mockGetAuthHeaders = getAuthHeaders as jest.MockedFunction<typeof getAuthHeaders>;

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe("useGroupBenefits", () => {
  const mockPageData: GroupBenefitsPageData = {
    id: 1,
    pageTitle: "Group Benefits",
    slug: "group-benefits",
    description: "Information about group benefits",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
    publishedAt: "2024-01-01T00:00:00.000Z",
    documentId: "page-1",
    locale: null,
    files: [
      {
        id: 1,
        title: "Benefits Guide",
        file: {
          id: 1,
          name: "benefits-guide.pdf",
          alternativeText: "Benefits Guide PDF",
          caption: "Employee benefits guide",
          width: null,
          height: null,
          formats: {},
          hash: "abc123",
          ext: ".pdf",
          mime: "application/pdf",
          size: 1024,
          url: "https://example.com/benefits-guide.pdf",
          previewUrl: null,
          provider: "local",
          provider_metadata: null,
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          documentId: "doc-1",
          locale: null,
          publishedAt: "2024-01-01T00:00:00.000Z",
          folderPath: null,
        },
      },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockGetAuthHeaders.mockReturnValue({
      "Authorization": "Bearer mock-token",
      "Content-Type": "application/json",
    });

    // Mock environment variable
    process.env.NEXT_PUBLIC_API_URL = "http://localhost:1339";
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("initialization", () => {
    it("should initialize with correct default state", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: false,
          initialized: false,
        },
      } as any);

      const { result } = renderHook(() => useGroupBenefits());

      expect(result.current.data).toBeNull();
      expect(result.current.isLoading).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it("should not fetch data when user is not authenticated", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: false,
          initialized: true,
        },
      } as any);

      renderHook(() => useGroupBenefits());

      expect(mockFetch).not.toHaveBeenCalled();
    });

    it("should not fetch data when auth is not initialized", () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: false,
        },
      } as any);

      renderHook(() => useGroupBenefits());

      expect(mockFetch).not.toHaveBeenCalled();
    });
  });

  describe("successful data fetching", () => {
    it("should fetch and set data successfully", async () => {
      const mockResponse: GroupBenefitsApiResponse = {
        data: mockPageData,
        meta: {},
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const { result } = renderHook(() => useGroupBenefits());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toEqual(mockPageData);
      expect(result.current.error).toBeNull();
      expect(mockFetch).toHaveBeenCalledWith(
        "http://localhost:1339/page-group-benefits",
        {
          headers: {
            "Authorization": "Bearer mock-token",
            "Content-Type": "application/json",
          },
          cache: 'no-store',
        }
      );
    });

    it("should handle empty files array", async () => {
      const mockEmptyData: GroupBenefitsPageData = {
        ...mockPageData,
        files: [],
      };

      const mockResponse: GroupBenefitsApiResponse = {
        data: mockEmptyData,
        meta: {},
      };

      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const { result } = renderHook(() => useGroupBenefits());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toEqual(mockEmptyData);
      expect(result.current.data?.files).toEqual([]);
      expect(result.current.error).toBeNull();
    });
  });

  describe("error handling", () => {
    it("should handle HTTP error responses", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: "Not Found",
      } as Response);

      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

      const { result } = renderHook(() => useGroupBenefits());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toBeNull();
      expect(result.current.error).toBe("Failed to fetch group benefits data");
      expect(consoleSpy).toHaveBeenCalledWith("Error fetching group benefits:", expect.any(Error));

      consoleSpy.mockRestore();
    });

    it("should handle network errors", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      const networkError = new Error("Network error");
      mockFetch.mockRejectedValueOnce(networkError);

      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

      const { result } = renderHook(() => useGroupBenefits());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toBeNull();
      expect(result.current.error).toBe("Network error");
      expect(consoleSpy).toHaveBeenCalledWith("Error fetching group benefits:", networkError);

      consoleSpy.mockRestore();
    });

    it("should handle non-Error exceptions", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockRejectedValueOnce("String error");

      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});

      const { result } = renderHook(() => useGroupBenefits());

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.data).toBeNull();
      expect(result.current.error).toBe("An error occurred");
      expect(consoleSpy).toHaveBeenCalledWith("Error fetching group benefits:", "String error");

      consoleSpy.mockRestore();
    });
  });

  describe("auth state changes", () => {
    it("should refetch data when auth state changes from unauthenticated to authenticated", async () => {
      const mockResponse: GroupBenefitsApiResponse = {
        data: mockPageData,
        meta: {},
      };

      // Start with unauthenticated state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: false,
          initialized: true,
        },
      } as any);

      const { result, rerender } = renderHook(() => useGroupBenefits());

      // Initially should not fetch when unauthenticated
      expect(mockFetch).not.toHaveBeenCalled();

      // Change to authenticated state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      rerender();

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result.current.data).toEqual(mockPageData);
    });

    it("should refetch data when initialization state changes", async () => {
      const mockResponse: GroupBenefitsApiResponse = {
        data: mockPageData,
        meta: {},
      };

      // Start with uninitialized state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: false,
        },
      } as any);

      const { result, rerender } = renderHook(() => useGroupBenefits());

      // Initially should not fetch when uninitialized
      expect(mockFetch).not.toHaveBeenCalled();

      // Change to initialized state
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      rerender();

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result.current.data).toEqual(mockPageData);
    });
  });

  describe("loading states", () => {
    it("should set loading to true when starting fetch", async () => {
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      // Create a promise that we can control
      let resolvePromise: (value: any) => void;
      const fetchPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      mockFetch.mockReturnValueOnce(fetchPromise as any);

      const { result } = renderHook(() => useGroupBenefits());

      // Initially loading should be true
      expect(result.current.isLoading).toBe(true);

      // Resolve the promise
      resolvePromise!({
        ok: true,
        json: async () => ({ data: mockPageData, meta: {} }),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });
    });

    it("should reset error state when starting new fetch", async () => {
      // Start with authenticated state that will fail
      mockUseAuthContext.mockReturnValue({
        userAuth: {
          isAuth: true,
          initialized: true,
        },
      } as any);

      // First request fails
      mockFetch.mockRejectedValueOnce(new Error("First error"));

      const { result, unmount } = renderHook(() => useGroupBenefits());

      await waitFor(() => {
        expect(result.current.error).toBe("First error");
      });

      // Unmount and remount with successful response
      unmount();

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: mockPageData, meta: {} }),
      } as Response);

      const { result: newResult } = renderHook(() => useGroupBenefits());

      await waitFor(() => {
        expect(newResult.current.error).toBeNull();
        expect(newResult.current.data).toEqual(mockPageData);
      });
    });
  });
});
