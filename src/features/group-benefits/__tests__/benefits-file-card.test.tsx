import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { BenefitsFileCard } from "../components/benefits-file-card";
import { saveAs } from "file-saver";
import type { GroupBenefitsFile } from "../types";

// Mock dependencies
jest.mock("file-saver");

const mockSaveAs = saveAs as jest.MockedFunction<typeof saveAs>;

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: jest.fn(),
});

describe("BenefitsFileCard", () => {
  const mockFile: GroupBenefitsFile = {
    id: 1,
    title: "Employee Benefits Guide 2024",
    file: {
      id: 1,
      name: "benefits-guide-2024.pdf",
      alternativeText: "Benefits Guide PDF",
      caption: "Employee benefits guide for 2024",
      width: null,
      height: null,
      formats: {},
      hash: "abc123def456",
      ext: ".pdf",
      mime: "application/pdf",
      size: 2048,
      url: "https://example.com/files/benefits-guide-2024.pdf",
      previewUrl: null,
      provider: "local",
      provider_metadata: null,
      createdAt: "2024-01-01T00:00:00.000Z",
      updatedAt: "2024-01-01T00:00:00.000Z",
      documentId: "doc-1",
      locale: null,
      publishedAt: "2024-01-01T00:00:00.000Z",
      folderPath: null,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("rendering", () => {
    it("should render file card with correct information", () => {
      render(<BenefitsFileCard file={mockFile} />);

      expect(screen.getByText("Employee Benefits Guide 2024")).toBeInTheDocument();
      expect(screen.getByText("benefits-guide-2024.pdf")).toBeInTheDocument();
      expect(screen.getByText("2.0 MB")).toBeInTheDocument();
      expect(screen.getByText("pdf")).toBeInTheDocument();
    });

    it("should render file icon", () => {
      render(<BenefitsFileCard file={mockFile} />);

      // The SVG doesn't have role="img", so find it by its class or other attributes
      const fileIcon = document.querySelector('svg[aria-hidden="true"]');
      expect(fileIcon).toBeInTheDocument();
      expect(fileIcon).toHaveClass("h-16", "w-16", "text-primary/60");
    });

    it("should render download and external link buttons", () => {
      render(<BenefitsFileCard file={mockFile} />);

      expect(screen.getByRole("button", { name: /download/i })).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "" })).toBeInTheDocument(); // External link button has no text
    });

    it("should display file extension badge", () => {
      render(<BenefitsFileCard file={mockFile} />);

      const badge = screen.getByText("pdf");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass("capitalize");
    });

    it("should handle file extension without dot", () => {
      const fileWithoutDot: GroupBenefitsFile = {
        ...mockFile,
        file: {
          ...mockFile.file,
          ext: "docx",
        },
      };

      render(<BenefitsFileCard file={fileWithoutDot} />);

      expect(screen.getByText("docx")).toBeInTheDocument();
    });

    it("should not render extension badge when ext is empty", () => {
      const fileWithoutExt: GroupBenefitsFile = {
        ...mockFile,
        file: {
          ...mockFile.file,
          ext: "",
        },
      };

      render(<BenefitsFileCard file={fileWithoutExt} />);

      expect(screen.queryByText("pdf")).not.toBeInTheDocument();
    });
  });

  describe("file size formatting", () => {
    it("should display size in KB for files smaller than 1MB", () => {
      const smallFile: GroupBenefitsFile = {
        ...mockFile,
        file: {
          ...mockFile.file,
          size: 512,
        },
      };

      render(<BenefitsFileCard file={smallFile} />);

      expect(screen.getByText("512.0 KB")).toBeInTheDocument();
    });

    it("should display size in MB for files larger than 1MB", () => {
      const largeFile: GroupBenefitsFile = {
        ...mockFile,
        file: {
          ...mockFile.file,
          size: 3072, // 3MB
        },
      };

      render(<BenefitsFileCard file={largeFile} />);

      expect(screen.getByText("3.0 MB")).toBeInTheDocument();
    });

    it("should handle decimal places correctly", () => {
      const fileWithDecimals: GroupBenefitsFile = {
        ...mockFile,
        file: {
          ...mockFile.file,
          size: 1536, // 1.5MB
        },
      };

      render(<BenefitsFileCard file={fileWithDecimals} />);

      expect(screen.getByText("1.5 MB")).toBeInTheDocument();
    });
  });

  describe("download functionality", () => {
    beforeEach(() => {
      // Mock fetch for download
      global.fetch = jest.fn();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it("should download file successfully", async () => {
      const user = userEvent.setup();
      const mockBlob = new Blob(["mock file content"], { type: "application/pdf" });
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        blob: () => Promise.resolve(mockBlob),
      });

      render(<BenefitsFileCard file={mockFile} />);

      const downloadButton = screen.getByRole("button", { name: /download/i });
      await user.click(downloadButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith("https://example.com/files/benefits-guide-2024.pdf");
        expect(mockSaveAs).toHaveBeenCalledWith(mockBlob, "employee benefits guide 2024.pdf");
      });
    });

    it("should generate clean filename from title", async () => {
      const user = userEvent.setup();
      const mockBlob = new Blob(["mock content"], { type: "application/pdf" });
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        blob: () => Promise.resolve(mockBlob),
      });

      const fileWithSpecialChars: GroupBenefitsFile = {
        ...mockFile,
        title: "Benefits Guide (2024) - Updated!",
      };

      render(<BenefitsFileCard file={fileWithSpecialChars} />);

      const downloadButton = screen.getByRole("button", { name: /download/i });
      await user.click(downloadButton);

      await waitFor(() => {
        expect(mockSaveAs).toHaveBeenCalledWith(mockBlob, "benefits guide -2024- - updated-.pdf");
      });
    });

    it("should handle download failure and fallback to window.open", async () => {
      const user = userEvent.setup();
      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});
      
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error("Network error"));

      render(<BenefitsFileCard file={mockFile} />);

      const downloadButton = screen.getByRole("button", { name: /download/i });
      await user.click(downloadButton);

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith("Download failed:", expect.any(Error));
        expect(window.open).toHaveBeenCalledWith("https://example.com/files/benefits-guide-2024.pdf", "_blank");
      });

      consoleSpy.mockRestore();
    });

    it("should handle HTTP error response and fallback to window.open", async () => {
      const user = userEvent.setup();
      const consoleSpy = jest.spyOn(console, "error").mockImplementation(() => {});
      
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: "Not Found",
      });

      render(<BenefitsFileCard file={mockFile} />);

      const downloadButton = screen.getByRole("button", { name: /download/i });
      await user.click(downloadButton);

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith("Download failed:", expect.any(Error));
        expect(window.open).toHaveBeenCalledWith("https://example.com/files/benefits-guide-2024.pdf", "_blank");
      });

      consoleSpy.mockRestore();
    });

    it("should not attempt download if file URL is missing", async () => {
      const user = userEvent.setup();
      
      const fileWithoutUrl: GroupBenefitsFile = {
        ...mockFile,
        file: {
          ...mockFile.file,
          url: "",
        },
      };

      render(<BenefitsFileCard file={fileWithoutUrl} />);

      const downloadButton = screen.getByRole("button", { name: /download/i });
      await user.click(downloadButton);

      expect(global.fetch).not.toHaveBeenCalled();
      expect(mockSaveAs).not.toHaveBeenCalled();
    });

    it("should not attempt download if title is missing", async () => {
      const user = userEvent.setup();
      
      const fileWithoutTitle: GroupBenefitsFile = {
        ...mockFile,
        title: "",
      };

      render(<BenefitsFileCard file={fileWithoutTitle} />);

      const downloadButton = screen.getByRole("button", { name: /download/i });
      await user.click(downloadButton);

      expect(global.fetch).not.toHaveBeenCalled();
      expect(mockSaveAs).not.toHaveBeenCalled();
    });
  });

  describe("external link functionality", () => {
    it("should open file in new tab when external link button is clicked", async () => {
      const user = userEvent.setup();

      render(<BenefitsFileCard file={mockFile} />);

      const externalLinkButton = screen.getByRole("button", { name: "" }); // External link button
      await user.click(externalLinkButton);

      expect(window.open).toHaveBeenCalledWith("https://example.com/files/benefits-guide-2024.pdf", "_blank");
    });
  });

  describe("accessibility", () => {
    it("should have proper ARIA attributes", () => {
      render(<BenefitsFileCard file={mockFile} />);

      const fileIcon = document.querySelector('svg[aria-hidden="true"]');
      expect(fileIcon).toHaveAttribute("aria-hidden", "true");
    });

    it("should have accessible button labels", () => {
      render(<BenefitsFileCard file={mockFile} />);

      expect(screen.getByRole("button", { name: /download/i })).toBeInTheDocument();
      // External link button should have an accessible name via icon or aria-label
      const externalButton = screen.getByRole("button", { name: "" });
      expect(externalButton).toBeInTheDocument();
    });
  });

  describe("styling and layout", () => {
    it("should apply hover effects", () => {
      render(<BenefitsFileCard file={mockFile} />);

      const card = screen.getByText("Employee Benefits Guide 2024").closest(".group");
      expect(card).toHaveClass("hover:shadow-lg", "hover:scale-[1.02]");
    });

    it("should have proper card structure", () => {
      render(<BenefitsFileCard file={mockFile} />);

      // Check for card components
      expect(screen.getByText("Employee Benefits Guide 2024").closest("[class*='card']")).toBeInTheDocument();
    });

    it("should display file icon in aspect-video container", () => {
      render(<BenefitsFileCard file={mockFile} />);

      const iconContainer = document.querySelector('.aspect-video');
      expect(iconContainer).toBeInTheDocument();
      expect(iconContainer).toHaveClass("bg-muted", "rounded-t-lg");

      // Check that the SVG icon is inside the container
      const fileIcon = iconContainer?.querySelector('svg[aria-hidden="true"]');
      expect(fileIcon).toBeInTheDocument();
    });
  });
});
