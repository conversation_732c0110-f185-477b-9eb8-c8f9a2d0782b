import type { GroupBenefitsFile, GroupBenefitsPageData, GroupBenefitsApiResponse } from "../types";

describe("Group Benefits Types", () => {
  describe("GroupBenefitsFile", () => {
    it("should have correct structure", () => {
      const mockFile: GroupBenefitsFile = {
        id: 1,
        title: "Test Benefits File",
        file: {
          id: 1,
          name: "benefits-guide.pdf",
          alternativeText: "Benefits Guide PDF",
          caption: "Employee benefits guide",
          width: null,
          height: null,
          formats: {},
          hash: "abc123",
          ext: ".pdf",
          mime: "application/pdf",
          size: 1024,
          url: "https://example.com/benefits-guide.pdf",
          previewUrl: null,
          provider: "local",
          provider_metadata: null,
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          documentId: "doc-1",
          locale: null,
          publishedAt: "2024-01-01T00:00:00.000Z",
          folderPath: null,
        },
      };

      expect(mockFile.id).toBe(1);
      expect(mockFile.title).toBe("Test Benefits File");
      expect(mockFile.file.name).toBe("benefits-guide.pdf");
      expect(mockFile.file.ext).toBe(".pdf");
      expect(mockFile.file.mime).toBe("application/pdf");
      expect(mockFile.file.size).toBe(1024);
      expect(mockFile.file.url).toBe("https://example.com/benefits-guide.pdf");
    });

    it("should allow optional file properties", () => {
      const mockFile: GroupBenefitsFile = {
        id: 2,
        title: "Another File",
        file: {
          id: 2,
          name: "document.docx",
          alternativeText: "",
          caption: "",
          width: 800,
          height: 600,
          formats: { thumbnail: { url: "thumb.jpg" } },
          hash: "def456",
          ext: ".docx",
          mime: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          size: 2048,
          url: "https://example.com/document.docx",
          previewUrl: "https://example.com/preview.jpg",
          provider: "aws-s3",
          provider_metadata: { bucket: "files" },
          createdAt: "2024-01-02T00:00:00.000Z",
          updatedAt: "2024-01-02T00:00:00.000Z",
          documentId: "doc-2",
          locale: "en",
          publishedAt: "2024-01-02T00:00:00.000Z",
          folderPath: "/documents",
        },
      };

      expect(mockFile.file.width).toBe(800);
      expect(mockFile.file.height).toBe(600);
      expect(mockFile.file.previewUrl).toBe("https://example.com/preview.jpg");
      expect(mockFile.file.locale).toBe("en");
      expect(mockFile.file.folderPath).toBe("/documents");
    });
  });

  describe("GroupBenefitsPageData", () => {
    it("should have correct structure", () => {
      const mockPageData: GroupBenefitsPageData = {
        id: 1,
        pageTitle: "Group Benefits",
        slug: "group-benefits",
        description: "Information about group benefits",
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        publishedAt: "2024-01-01T00:00:00.000Z",
        documentId: "page-1",
        locale: null,
        files: [],
      };

      expect(mockPageData.id).toBe(1);
      expect(mockPageData.pageTitle).toBe("Group Benefits");
      expect(mockPageData.slug).toBe("group-benefits");
      expect(mockPageData.description).toBe("Information about group benefits");
      expect(Array.isArray(mockPageData.files)).toBe(true);
    });

    it("should allow files array with GroupBenefitsFile items", () => {
      const mockFile: GroupBenefitsFile = {
        id: 1,
        title: "Benefits Guide",
        file: {
          id: 1,
          name: "guide.pdf",
          alternativeText: "",
          caption: "",
          width: null,
          height: null,
          formats: {},
          hash: "abc123",
          ext: ".pdf",
          mime: "application/pdf",
          size: 1024,
          url: "https://example.com/guide.pdf",
          previewUrl: null,
          provider: "local",
          provider_metadata: null,
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          documentId: "doc-1",
          locale: null,
          publishedAt: "2024-01-01T00:00:00.000Z",
          folderPath: null,
        },
      };

      const mockPageData: GroupBenefitsPageData = {
        id: 1,
        pageTitle: "Group Benefits",
        slug: "group-benefits",
        description: "Information about group benefits",
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        publishedAt: "2024-01-01T00:00:00.000Z",
        documentId: "page-1",
        locale: null,
        files: [mockFile],
      };

      expect(mockPageData.files).toHaveLength(1);
      expect(mockPageData.files[0]).toEqual(mockFile);
    });

    it("should allow null publishedAt", () => {
      const mockPageData: GroupBenefitsPageData = {
        id: 1,
        pageTitle: "Draft Page",
        slug: "draft-page",
        description: "Draft description",
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
        publishedAt: null,
        documentId: "page-1",
        locale: "en",
        files: [],
      };

      expect(mockPageData.publishedAt).toBeNull();
      expect(mockPageData.locale).toBe("en");
    });
  });

  describe("GroupBenefitsApiResponse", () => {
    it("should have correct structure", () => {
      const mockResponse: GroupBenefitsApiResponse = {
        data: {
          id: 1,
          pageTitle: "Group Benefits",
          slug: "group-benefits",
          description: "Information about group benefits",
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          publishedAt: "2024-01-01T00:00:00.000Z",
          documentId: "page-1",
          locale: null,
          files: [],
        },
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 1,
          },
        },
      };

      expect(mockResponse.data).toBeDefined();
      expect(mockResponse.meta).toBeDefined();
      expect(mockResponse.data.pageTitle).toBe("Group Benefits");
      expect(mockResponse.meta.pagination).toBeDefined();
    });

    it("should allow empty meta object", () => {
      const mockResponse: GroupBenefitsApiResponse = {
        data: {
          id: 1,
          pageTitle: "Group Benefits",
          slug: "group-benefits",
          description: "Information about group benefits",
          createdAt: "2024-01-01T00:00:00.000Z",
          updatedAt: "2024-01-01T00:00:00.000Z",
          publishedAt: "2024-01-01T00:00:00.000Z",
          documentId: "page-1",
          locale: null,
          files: [],
        },
        meta: {},
      };

      expect(mockResponse.meta).toEqual({});
    });
  });
});
