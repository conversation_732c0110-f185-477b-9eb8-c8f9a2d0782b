import React from 'react';

const AnimatedGradientBackground = ({ className = '' }) => {
  return (
    <div className={`animated-gradient-bg ${className}`}>
      <style jsx>{`
        .animated-gradient-bg {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          z-index: -1;
          background: linear-gradient(
            45deg,
            #ff6b6b,
            #4ecdc4,
            #45b7d1,
            #96ceb4,
            #feca57,
            #ff9ff3,
            #54a0ff,
            #5f27cd
          );
          background-size: 400% 400%;
          animation: gradientShift 25s ease infinite;
        }

        @keyframes gradientShift {
          0% {
            background-position: 0% 50%;
          }
          25% {
            background-position: 100% 50%;
          }
          50% {
            background-position: 100% 100%;
          }
          75% {
            background-position: 50% 100%;
          }
          100% {
            background-position: 0% 50%;
          }
        }

        /* Additional floating color orbs for more dynamic effect */
        .animated-gradient-bg::before,
        .animated-gradient-bg::after {
          content: '';
          position: absolute;
          border-radius: 50%;
          filter: blur(60px);
          opacity: 0.7;
          animation: float 30s ease-in-out infinite;
        }

        .animated-gradient-bg::before {
          top: 20%;
          left: 20%;
          width: 300px;
          height: 300px;
          background: radial-gradient(circle, #ff6b6b, transparent);
          animation: float 30s ease-in-out infinite;
        }

        .animated-gradient-bg::after {
          bottom: 20%;
          right: 20%;
          width: 400px;
          height: 400px;
          background: radial-gradient(circle, #FE8302, transparent);
          animation: float 35s ease-in-out infinite reverse;
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1);
          }
          25% {
            transform: translateY(-30px) translateX(20px) scale(1.1);
          }
          50% {
            transform: translateY(-60px) translateX(-20px) scale(0.9);
          }
          75% {
            transform: translateY(-30px) translateX(-40px) scale(1.05);
          }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
          .animated-gradient-bg::before {
            width: 200px;
            height: 200px;
          }
          
          .animated-gradient-bg::after {
            width: 250px;
            height: 250px;
          }
        }
      `}</style>
    </div>
  );
};

export default AnimatedGradientBackground;