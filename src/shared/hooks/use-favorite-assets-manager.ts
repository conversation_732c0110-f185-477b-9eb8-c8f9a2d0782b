"use client";

import { useState, useCallback } from "react";
import { apiClient } from "@/shared/lib/api";
import { getCookie } from "@/shared/lib/auth";
import { useAuthContext } from "@/shared/contexts/auth-context";

interface UseFavoriteAssetsManagerResult {
  favoriteAssets: string[];
  isLoading: boolean;
  error: string | null;
  addToFavorites: (documentId: string) => Promise<boolean>;
  removeFromFavorites: (documentId: string) => Promise<boolean>;
  isFavorite: (documentId: string) => boolean;
  toggleFavorite: (documentId: string) => Promise<boolean>;
}

/**
 * Shared hook for managing user's favorite assets across the application
 */
export function useFavoriteAssetsManager(): UseFavoriteAssetsManagerResult {
  const { userAuth, refreshUserData } = useAuthContext();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const favoriteAssets = userAuth.userInfo?.favoriteAssets || [];

  const updateFavoriteAssets = useCallback(async (newFavoriteAssets: string[]): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      const userId = getCookie("userId");
      if (!userId) {
        setError("User not authenticated");
        return false;
      }

      const response = await apiClient.put<any>(`/users/${userId}`, {
        favoriteAssets: newFavoriteAssets
      });

      if (response.success) {
        // Refresh user data to update the context
        await refreshUserData();
        return true;
      } else {
        setError(response.error || "Failed to update favorite assets");
        return false;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update favorite assets";
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [refreshUserData]);

  const addToFavorites = useCallback(async (documentId: string): Promise<boolean> => {
    if (favoriteAssets.includes(documentId)) {
      return true; // Already in favorites
    }

    const newFavoriteAssets = [...favoriteAssets, documentId];
    return await updateFavoriteAssets(newFavoriteAssets);
  }, [favoriteAssets, updateFavoriteAssets]);

  const removeFromFavorites = useCallback(async (documentId: string): Promise<boolean> => {
    if (!favoriteAssets.includes(documentId)) {
      return true; // Not in favorites
    }

    const newFavoriteAssets = favoriteAssets.filter(id => id !== documentId);
    return await updateFavoriteAssets(newFavoriteAssets);
  }, [favoriteAssets, updateFavoriteAssets]);

  const isFavorite = useCallback((documentId: string): boolean => {
    return favoriteAssets.includes(documentId);
  }, [favoriteAssets]);

  const toggleFavorite = useCallback(async (documentId: string): Promise<boolean> => {
    if (isFavorite(documentId)) {
      return await removeFromFavorites(documentId);
    } else {
      return await addToFavorites(documentId);
    }
  }, [isFavorite, addToFavorites, removeFromFavorites]);

  return {
    favoriteAssets,
    isLoading,
    error,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    toggleFavorite
  };
}
